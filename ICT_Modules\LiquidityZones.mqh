//+------------------------------------------------------------------+
//|                                               LiquidityZones.mqh |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"

#ifndef LIQUIDITY_ZONES_MQH
#define LIQUIDITY_ZONES_MQH

//+------------------------------------------------------------------+
//| Liquidity Zone Types                                             |
//+------------------------------------------------------------------+
enum ENUM_LIQUIDITY_TYPE
{
    LIQUIDITY_NONE,
    LIQUIDITY_BUY_SIDE,     // Above recent highs
    LIQUIDITY_SELL_SIDE,    // Below recent lows
    LIQUIDITY_EQUAL_HIGHS,  // Equal highs formation
    LIQUIDITY_EQUAL_LOWS    // Equal lows formation
};

enum ENUM_LIQUIDITY_STATUS
{
    LIQUIDITY_ACTIVE,
    LIQUIDITY_SWEPT,
    LIQUIDITY_EXPIRED
};

//+------------------------------------------------------------------+
//| Liquidity Zone Structure                                         |
//+------------------------------------------------------------------+
struct SLiquidityZone
{
    datetime creationTime;
    double price;
    ENUM_LIQUIDITY_TYPE type;
    ENUM_LIQUIDITY_STATUS status;
    int strength;           // Number of touches or equal levels
    double buffer;          // Buffer zone around the level
    datetime lastTouchTime;
    bool isSwept;
    datetime sweptTime;
    int barIndex;
};

//+------------------------------------------------------------------+
//| Liquidity Zones Class                                            |
//+------------------------------------------------------------------+
class CLiquidityZones
{
private:
    string m_symbol;
    int m_lookback;
    double m_buffer;
    
    SLiquidityZone m_liquidityZones[];
    SLiquidityZone m_recentSweeps[];
    
    double m_equalHighsLevel;
    double m_equalLowsLevel;
    int m_equalHighsCount;
    int m_equalLowsCount;
    datetime m_lastEqualHighsTime;
    datetime m_lastEqualLowsTime;
    
public:
    CLiquidityZones();
    ~CLiquidityZones();
    
    bool Initialize(string symbol, int lookback, double buffer);
    void Update();
    
    // Liquidity detection methods
    bool DetectBuySideLiquidity();
    bool DetectSellSideLiquidity();
    bool DetectEqualHighs();
    bool DetectEqualLows();
    bool CheckLiquiditySweep();
    
    // Getter methods
    int GetLiquidityZonesCount() { return ArraySize(m_liquidityZones); }
    SLiquidityZone GetLiquidityZone(int index);
    bool IsLiquiditySweptRecently(ENUM_LIQUIDITY_TYPE type, int barsBack = 10);
    
    double GetNearestBuySideLiquidity();
    double GetNearestSellSideLiquidity();
    bool IsEqualHighsPresent() { return m_equalHighsCount >= 2; }
    bool IsEqualLowsPresent() { return m_equalLowsCount >= 2; }
    
    // Utility methods
    bool IsLiquidityLevel(double price, ENUM_LIQUIDITY_TYPE type);
    double GetLiquidityStrength(double price, ENUM_LIQUIDITY_TYPE type);
    
private:
    void AddLiquidityZone(SLiquidityZone &zone);
    void UpdateLiquidityStatus();
    void CleanupExpiredZones();
    bool IsEqualLevel(double price1, double price2, double tolerance);
    double GetPointValue();
    int CountTouchesAtLevel(double price, int lookbackBars);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CLiquidityZones::CLiquidityZones()
{
    m_symbol = "";
    m_lookback = 100;
    m_buffer = 2.0;
    
    m_equalHighsLevel = 0;
    m_equalLowsLevel = 0;
    m_equalHighsCount = 0;
    m_equalLowsCount = 0;
    m_lastEqualHighsTime = 0;
    m_lastEqualLowsTime = 0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CLiquidityZones::~CLiquidityZones()
{
    ArrayFree(m_liquidityZones);
    ArrayFree(m_recentSweeps);
}

//+------------------------------------------------------------------+
//| Initialize                                                       |
//+------------------------------------------------------------------+
bool CLiquidityZones::Initialize(string symbol, int lookback, double buffer)
{
    m_symbol = symbol;
    m_lookback = lookback;
    m_buffer = buffer;
    
    ArrayResize(m_liquidityZones, 0);
    ArrayResize(m_recentSweeps, 0);
    
    return true;
}

//+------------------------------------------------------------------+
//| Update liquidity zones analysis                                  |
//+------------------------------------------------------------------+
void CLiquidityZones::Update()
{
    // Detect buy-side liquidity
    DetectBuySideLiquidity();
    
    // Detect sell-side liquidity
    DetectSellSideLiquidity();
    
    // Detect equal highs and lows
    DetectEqualHighs();
    DetectEqualLows();
    
    // Check for liquidity sweeps
    CheckLiquiditySweep();
    
    // Update status of existing zones
    UpdateLiquidityStatus();
    
    // Clean up expired zones
    CleanupExpiredZones();
}

//+------------------------------------------------------------------+
//| Detect buy-side liquidity                                        |
//+------------------------------------------------------------------+
bool CLiquidityZones::DetectBuySideLiquidity()
{
    double highestHigh = 0;
    int highestBar = -1;
    
    // Find the highest high in the lookback period
    for(int i = 1; i <= m_lookback; i++)
    {
        double high = iHigh(m_symbol, PERIOD_CURRENT, i);
        if(high > highestHigh)
        {
            highestHigh = high;
            highestBar = i;
        }
    }
    
    if(highestBar == -1)
        return false;
    
    // Check if this level has multiple touches (liquidity buildup)
    int touches = CountTouchesAtLevel(highestHigh, m_lookback);
    
    if(touches >= 2)
    {
        // Create buy-side liquidity zone
        SLiquidityZone zone;
        zone.creationTime = iTime(m_symbol, PERIOD_CURRENT, highestBar);
        zone.price = highestHigh;
        zone.type = LIQUIDITY_BUY_SIDE;
        zone.status = LIQUIDITY_ACTIVE;
        zone.strength = touches;
        zone.buffer = m_buffer * GetPointValue();
        zone.lastTouchTime = TimeCurrent();
        zone.isSwept = false;
        zone.barIndex = highestBar;
        
        // Check if this zone already exists
        bool exists = false;
        for(int i = 0; i < ArraySize(m_liquidityZones); i++)
        {
            if(m_liquidityZones[i].type == LIQUIDITY_BUY_SIDE &&
               IsEqualLevel(m_liquidityZones[i].price, highestHigh, zone.buffer))
            {
                exists = true;
                // Update existing zone
                m_liquidityZones[i].strength = MathMax(m_liquidityZones[i].strength, touches);
                m_liquidityZones[i].lastTouchTime = TimeCurrent();
                break;
            }
        }
        
        if(!exists)
        {
            AddLiquidityZone(zone);
        }
        
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Detect sell-side liquidity                                       |
//+------------------------------------------------------------------+
bool CLiquidityZones::DetectSellSideLiquidity()
{
    double lowestLow = DBL_MAX;
    int lowestBar = -1;
    
    // Find the lowest low in the lookback period
    for(int i = 1; i <= m_lookback; i++)
    {
        double low = iLow(m_symbol, PERIOD_CURRENT, i);
        if(low < lowestLow)
        {
            lowestLow = low;
            lowestBar = i;
        }
    }
    
    if(lowestBar == -1)
        return false;
    
    // Check if this level has multiple touches (liquidity buildup)
    int touches = CountTouchesAtLevel(lowestLow, m_lookback);
    
    if(touches >= 2)
    {
        // Create sell-side liquidity zone
        SLiquidityZone zone;
        zone.creationTime = iTime(m_symbol, PERIOD_CURRENT, lowestBar);
        zone.price = lowestLow;
        zone.type = LIQUIDITY_SELL_SIDE;
        zone.status = LIQUIDITY_ACTIVE;
        zone.strength = touches;
        zone.buffer = m_buffer * GetPointValue();
        zone.lastTouchTime = TimeCurrent();
        zone.isSwept = false;
        zone.barIndex = lowestBar;
        
        // Check if this zone already exists
        bool exists = false;
        for(int i = 0; i < ArraySize(m_liquidityZones); i++)
        {
            if(m_liquidityZones[i].type == LIQUIDITY_SELL_SIDE &&
               IsEqualLevel(m_liquidityZones[i].price, lowestLow, zone.buffer))
            {
                exists = true;
                // Update existing zone
                m_liquidityZones[i].strength = MathMax(m_liquidityZones[i].strength, touches);
                m_liquidityZones[i].lastTouchTime = TimeCurrent();
                break;
            }
        }
        
        if(!exists)
        {
            AddLiquidityZone(zone);
        }
        
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Detect equal highs                                               |
//+------------------------------------------------------------------+
bool CLiquidityZones::DetectEqualHighs()
{
    double tolerance = m_buffer * GetPointValue();
    m_equalHighsCount = 0;
    
    // Look for equal highs in recent bars
    for(int i = 1; i <= 20; i++)
    {
        double high1 = iHigh(m_symbol, PERIOD_CURRENT, i);
        
        for(int j = i + 1; j <= m_lookback; j++)
        {
            double high2 = iHigh(m_symbol, PERIOD_CURRENT, j);
            
            if(IsEqualLevel(high1, high2, tolerance))
            {
                m_equalHighsLevel = high1;
                m_equalHighsCount++;
                m_lastEqualHighsTime = iTime(m_symbol, PERIOD_CURRENT, i);
                
                // Create equal highs liquidity zone
                SLiquidityZone zone;
                zone.creationTime = m_lastEqualHighsTime;
                zone.price = m_equalHighsLevel;
                zone.type = LIQUIDITY_EQUAL_HIGHS;
                zone.status = LIQUIDITY_ACTIVE;
                zone.strength = m_equalHighsCount;
                zone.buffer = tolerance;
                zone.lastTouchTime = TimeCurrent();
                zone.isSwept = false;
                zone.barIndex = i;
                
                AddLiquidityZone(zone);
                return true;
            }
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Detect equal lows                                                |
//+------------------------------------------------------------------+
bool CLiquidityZones::DetectEqualLows()
{
    double tolerance = m_buffer * GetPointValue();
    m_equalLowsCount = 0;
    
    // Look for equal lows in recent bars
    for(int i = 1; i <= 20; i++)
    {
        double low1 = iLow(m_symbol, PERIOD_CURRENT, i);
        
        for(int j = i + 1; j <= m_lookback; j++)
        {
            double low2 = iLow(m_symbol, PERIOD_CURRENT, j);
            
            if(IsEqualLevel(low1, low2, tolerance))
            {
                m_equalLowsLevel = low1;
                m_equalLowsCount++;
                m_lastEqualLowsTime = iTime(m_symbol, PERIOD_CURRENT, i);
                
                // Create equal lows liquidity zone
                SLiquidityZone zone;
                zone.creationTime = m_lastEqualLowsTime;
                zone.price = m_equalLowsLevel;
                zone.type = LIQUIDITY_EQUAL_LOWS;
                zone.status = LIQUIDITY_ACTIVE;
                zone.strength = m_equalLowsCount;
                zone.buffer = tolerance;
                zone.lastTouchTime = TimeCurrent();
                zone.isSwept = false;
                zone.barIndex = i;
                
                AddLiquidityZone(zone);
                return true;
            }
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Check for liquidity sweep                                        |
//+------------------------------------------------------------------+
bool CLiquidityZones::CheckLiquiditySweep()
{
    double currentPrice = SymbolInfoDouble(m_symbol, SYMBOL_BID);
    bool sweepDetected = false;
    
    for(int i = 0; i < ArraySize(m_liquidityZones); i++)
    {
        if(m_liquidityZones[i].status != LIQUIDITY_ACTIVE)
            continue;
        
        // Check for buy-side liquidity sweep
        if(m_liquidityZones[i].type == LIQUIDITY_BUY_SIDE || 
           m_liquidityZones[i].type == LIQUIDITY_EQUAL_HIGHS)
        {
            if(currentPrice > m_liquidityZones[i].price + m_liquidityZones[i].buffer)
            {
                m_liquidityZones[i].status = LIQUIDITY_SWEPT;
                m_liquidityZones[i].isSwept = true;
                m_liquidityZones[i].sweptTime = TimeCurrent();
                
                // Add to recent sweeps
                int size = ArraySize(m_recentSweeps);
                ArrayResize(m_recentSweeps, size + 1);
                m_recentSweeps[size] = m_liquidityZones[i];
                
                sweepDetected = true;
            }
        }
        
        // Check for sell-side liquidity sweep
        if(m_liquidityZones[i].type == LIQUIDITY_SELL_SIDE || 
           m_liquidityZones[i].type == LIQUIDITY_EQUAL_LOWS)
        {
            if(currentPrice < m_liquidityZones[i].price - m_liquidityZones[i].buffer)
            {
                m_liquidityZones[i].status = LIQUIDITY_SWEPT;
                m_liquidityZones[i].isSwept = true;
                m_liquidityZones[i].sweptTime = TimeCurrent();
                
                // Add to recent sweeps
                int size = ArraySize(m_recentSweeps);
                ArrayResize(m_recentSweeps, size + 1);
                m_recentSweeps[size] = m_liquidityZones[i];
                
                sweepDetected = true;
            }
        }
    }
    
    return sweepDetected;
}

//+------------------------------------------------------------------+
//| Get liquidity zone by index                                      |
//+------------------------------------------------------------------+
SLiquidityZone CLiquidityZones::GetLiquidityZone(int index)
{
    SLiquidityZone emptyZone;
    ZeroMemory(emptyZone);
    
    if(index >= 0 && index < ArraySize(m_liquidityZones))
        return m_liquidityZones[index];
    
    return emptyZone;
}

//+------------------------------------------------------------------+
//| Check if liquidity was swept recently                            |
//+------------------------------------------------------------------+
bool CLiquidityZones::IsLiquiditySweptRecently(ENUM_LIQUIDITY_TYPE type, int barsBack = 10)
{
    datetime cutoffTime = iTime(m_symbol, PERIOD_CURRENT, barsBack);
    
    for(int i = 0; i < ArraySize(m_recentSweeps); i++)
    {
        if(m_recentSweeps[i].type == type && m_recentSweeps[i].sweptTime >= cutoffTime)
            return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Get nearest buy-side liquidity                                   |
//+------------------------------------------------------------------+
double CLiquidityZones::GetNearestBuySideLiquidity()
{
    double currentPrice = SymbolInfoDouble(m_symbol, SYMBOL_BID);
    double nearestLevel = 0;
    double minDistance = DBL_MAX;
    
    for(int i = 0; i < ArraySize(m_liquidityZones); i++)
    {
        if(m_liquidityZones[i].status == LIQUIDITY_ACTIVE &&
           (m_liquidityZones[i].type == LIQUIDITY_BUY_SIDE || 
            m_liquidityZones[i].type == LIQUIDITY_EQUAL_HIGHS))
        {
            double distance = m_liquidityZones[i].price - currentPrice;
            if(distance > 0 && distance < minDistance)
            {
                minDistance = distance;
                nearestLevel = m_liquidityZones[i].price;
            }
        }
    }
    
    return nearestLevel;
}

//+------------------------------------------------------------------+
//| Get nearest sell-side liquidity                                  |
//+------------------------------------------------------------------+
double CLiquidityZones::GetNearestSellSideLiquidity()
{
    double currentPrice = SymbolInfoDouble(m_symbol, SYMBOL_BID);
    double nearestLevel = 0;
    double minDistance = DBL_MAX;
    
    for(int i = 0; i < ArraySize(m_liquidityZones); i++)
    {
        if(m_liquidityZones[i].status == LIQUIDITY_ACTIVE &&
           (m_liquidityZones[i].type == LIQUIDITY_SELL_SIDE || 
            m_liquidityZones[i].type == LIQUIDITY_EQUAL_LOWS))
        {
            double distance = currentPrice - m_liquidityZones[i].price;
            if(distance > 0 && distance < minDistance)
            {
                minDistance = distance;
                nearestLevel = m_liquidityZones[i].price;
            }
        }
    }
    
    return nearestLevel;
}

//+------------------------------------------------------------------+
//| Add liquidity zone to array                                      |
//+------------------------------------------------------------------+
void CLiquidityZones::AddLiquidityZone(SLiquidityZone &zone)
{
    int size = ArraySize(m_liquidityZones);
    ArrayResize(m_liquidityZones, size + 1);
    m_liquidityZones[size] = zone;
}

//+------------------------------------------------------------------+
//| Update liquidity status                                          |
//+------------------------------------------------------------------+
void CLiquidityZones::UpdateLiquidityStatus()
{
    // Status is updated in CheckLiquiditySweep method
}

//+------------------------------------------------------------------+
//| Clean up expired zones                                           |
//+------------------------------------------------------------------+
void CLiquidityZones::CleanupExpiredZones()
{
    datetime cutoffTime = iTime(m_symbol, PERIOD_CURRENT, m_lookback * 2);
    
    for(int i = ArraySize(m_liquidityZones) - 1; i >= 0; i--)
    {
        if(m_liquidityZones[i].creationTime < cutoffTime)
        {
            // Remove expired zone
            for(int j = i; j < ArraySize(m_liquidityZones) - 1; j++)
            {
                m_liquidityZones[j] = m_liquidityZones[j + 1];
            }
            ArrayResize(m_liquidityZones, ArraySize(m_liquidityZones) - 1);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if two levels are equal within tolerance                   |
//+------------------------------------------------------------------+
bool CLiquidityZones::IsEqualLevel(double price1, double price2, double tolerance)
{
    return MathAbs(price1 - price2) <= tolerance;
}

//+------------------------------------------------------------------+
//| Get point value for the symbol                                   |
//+------------------------------------------------------------------+
double CLiquidityZones::GetPointValue()
{
    return SymbolInfoDouble(m_symbol, SYMBOL_POINT);
}

//+------------------------------------------------------------------+
//| Count touches at a specific level                                |
//+------------------------------------------------------------------+
int CLiquidityZones::CountTouchesAtLevel(double price, int lookbackBars)
{
    int touches = 0;
    double tolerance = m_buffer * GetPointValue();

    for(int i = 1; i <= lookbackBars; i++)
    {
        double high = iHigh(m_symbol, PERIOD_CURRENT, i);
        double low = iLow(m_symbol, PERIOD_CURRENT, i);

        if(IsEqualLevel(high, price, tolerance) || IsEqualLevel(low, price, tolerance))
        {
            touches++;
        }
    }

    return touches;
}

#endif // LIQUIDITY_ZONES_MQH
