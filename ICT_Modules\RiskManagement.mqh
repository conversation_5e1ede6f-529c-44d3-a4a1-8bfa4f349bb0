//+------------------------------------------------------------------+
//|                                              RiskManagement.mqh |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"

#ifndef RISK_MANAGEMENT_MQH
#define RISK_MANAGEMENT_MQH

//+------------------------------------------------------------------+
//| Risk Management Class                                            |
//+------------------------------------------------------------------+
class CRiskManagement
{
private:
    string m_symbol;
    double m_riskPercent;
    double m_minRiskReward;
    double m_maxRiskReward;
    
    double m_accountBalance;
    double m_accountEquity;
    double m_maxDailyLoss;
    double m_maxWeeklyLoss;
    double m_currentDailyLoss;
    double m_currentWeeklyLoss;
    
    int m_maxTradesPerDay;
    int m_maxOpenTrades;
    int m_currentTradesCount;
    
    double m_minLotSize;
    double m_maxLotSize;
    double m_lotStep;
    
public:
    CRiskManagement();
    ~CRiskManagement();
    
    bool Initialize(string symbol, double riskPercent, double minRR, double maxRR);
    void Update();
    
    // Lot size calculation
    double CalculateLotSize(double entryPrice, double stopLoss);
    double CalculateLotSizeByRisk(double riskAmount, double entryPrice, double stopLoss);
    double NormalizeLotSize(double lotSize);
    
    // Risk validation
    bool ValidateRiskReward(double entryPrice, double stopLoss, double takeProfit);
    bool CanOpenNewTrade();
    bool IsWithinDailyLoss();
    bool IsWithinWeeklyLoss();
    bool IsMaxTradesReached();
    
    // Position sizing
    double GetMaxRiskAmount();
    double GetRiskAmount(double lotSize, double entryPrice, double stopLoss);
    double GetPotentialProfit(double lotSize, double entryPrice, double takeProfit);
    
    // Trailing stop calculations
    double CalculateTrailingStop(ENUM_ORDER_TYPE orderType, double currentPrice, double atrValue, double multiplier);
    double CalculateATRTrailingStop(ENUM_ORDER_TYPE orderType, double currentPrice, int atrPeriod, double multiplier);
    
    // Account protection
    void UpdateDailyLoss(double loss);
    void UpdateWeeklyLoss(double loss);
    void ResetDailyCounters();
    void ResetWeeklyCounters();
    
    // Getters
    double GetRiskPercent() { return m_riskPercent; }
    double GetMinRiskReward() { return m_minRiskReward; }
    double GetMaxRiskReward() { return m_maxRiskReward; }
    double GetAccountBalance() { return m_accountBalance; }
    double GetCurrentDailyLoss() { return m_currentDailyLoss; }
    double GetCurrentWeeklyLoss() { return m_currentWeeklyLoss; }
    int GetCurrentTradesCount() { return m_currentTradesCount; }
    
    // Setters
    void SetRiskPercent(double riskPercent) { m_riskPercent = riskPercent; }
    void SetMaxDailyLoss(double maxLoss) { m_maxDailyLoss = maxLoss; }
    void SetMaxWeeklyLoss(double maxLoss) { m_maxWeeklyLoss = maxLoss; }
    void SetMaxTradesPerDay(int maxTrades) { m_maxTradesPerDay = maxTrades; }
    void SetMaxOpenTrades(int maxTrades) { m_maxOpenTrades = maxTrades; }
    
private:
    void UpdateAccountInfo();
    void UpdateSymbolInfo();
    double GetPointValue();
    double GetTickValue();
    double GetContractSize();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CRiskManagement::CRiskManagement()
{
    m_symbol = "";
    m_riskPercent = 1.0;
    m_minRiskReward = 2.0;
    m_maxRiskReward = 3.0;
    
    m_accountBalance = 0;
    m_accountEquity = 0;
    m_maxDailyLoss = 0;
    m_maxWeeklyLoss = 0;
    m_currentDailyLoss = 0;
    m_currentWeeklyLoss = 0;
    
    m_maxTradesPerDay = 5;
    m_maxOpenTrades = 3;
    m_currentTradesCount = 0;
    
    m_minLotSize = 0;
    m_maxLotSize = 0;
    m_lotStep = 0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CRiskManagement::~CRiskManagement()
{
}

//+------------------------------------------------------------------+
//| Initialize                                                       |
//+------------------------------------------------------------------+
bool CRiskManagement::Initialize(string symbol, double riskPercent, double minRR, double maxRR)
{
    m_symbol = symbol;
    m_riskPercent = riskPercent;
    m_minRiskReward = minRR;
    m_maxRiskReward = maxRR;
    
    // Update account and symbol information
    UpdateAccountInfo();
    UpdateSymbolInfo();
    
    // Set default risk limits
    m_maxDailyLoss = m_accountBalance * 0.05;  // 5% max daily loss
    m_maxWeeklyLoss = m_accountBalance * 0.10; // 10% max weekly loss
    
    return true;
}

//+------------------------------------------------------------------+
//| Update risk management                                           |
//+------------------------------------------------------------------+
void CRiskManagement::Update()
{
    UpdateAccountInfo();
    
    // Count current open trades
    m_currentTradesCount = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == m_symbol)
            m_currentTradesCount++;
    }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                      |
//+------------------------------------------------------------------+
double CRiskManagement::CalculateLotSize(double entryPrice, double stopLoss)
{
    double riskAmount = GetMaxRiskAmount();
    return CalculateLotSizeByRisk(riskAmount, entryPrice, stopLoss);
}

//+------------------------------------------------------------------+
//| Calculate lot size by risk amount                                |
//+------------------------------------------------------------------+
double CRiskManagement::CalculateLotSizeByRisk(double riskAmount, double entryPrice, double stopLoss)
{
    if(entryPrice <= 0 || stopLoss <= 0 || riskAmount <= 0)
        return 0;
    
    double stopLossDistance = MathAbs(entryPrice - stopLoss);
    if(stopLossDistance <= 0)
        return 0;
    
    double tickValue = GetTickValue();
    double pointValue = GetPointValue();
    
    if(tickValue <= 0 || pointValue <= 0)
        return 0;
    
    // Calculate lot size
    double lotSize = riskAmount / (stopLossDistance * tickValue / pointValue);
    
    // Normalize lot size
    return NormalizeLotSize(lotSize);
}

//+------------------------------------------------------------------+
//| Normalize lot size to valid values                               |
//+------------------------------------------------------------------+
double CRiskManagement::NormalizeLotSize(double lotSize)
{
    if(lotSize < m_minLotSize)
        return m_minLotSize;
    
    if(lotSize > m_maxLotSize)
        return m_maxLotSize;
    
    // Round to lot step
    double normalizedLot = MathRound(lotSize / m_lotStep) * m_lotStep;
    
    return MathMax(normalizedLot, m_minLotSize);
}

//+------------------------------------------------------------------+
//| Validate risk-reward ratio                                       |
//+------------------------------------------------------------------+
bool CRiskManagement::ValidateRiskReward(double entryPrice, double stopLoss, double takeProfit)
{
    if(entryPrice <= 0 || stopLoss <= 0 || takeProfit <= 0)
        return false;
    
    double risk = MathAbs(entryPrice - stopLoss);
    double reward = MathAbs(takeProfit - entryPrice);
    
    if(risk <= 0)
        return false;
    
    double riskRewardRatio = reward / risk;
    
    return (riskRewardRatio >= m_minRiskReward && riskRewardRatio <= m_maxRiskReward);
}

//+------------------------------------------------------------------+
//| Check if new trade can be opened                                 |
//+------------------------------------------------------------------+
bool CRiskManagement::CanOpenNewTrade()
{
    // Check daily loss limit
    if(!IsWithinDailyLoss())
        return false;
    
    // Check weekly loss limit
    if(!IsWithinWeeklyLoss())
        return false;
    
    // Check maximum trades per day
    if(IsMaxTradesReached())
        return false;
    
    // Check maximum open trades
    if(m_currentTradesCount >= m_maxOpenTrades)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if within daily loss limit                                 |
//+------------------------------------------------------------------+
bool CRiskManagement::IsWithinDailyLoss()
{
    return (m_currentDailyLoss < m_maxDailyLoss);
}

//+------------------------------------------------------------------+
//| Check if within weekly loss limit                                |
//+------------------------------------------------------------------+
bool CRiskManagement::IsWithinWeeklyLoss()
{
    return (m_currentWeeklyLoss < m_maxWeeklyLoss);
}

//+------------------------------------------------------------------+
//| Check if maximum trades reached                                  |
//+------------------------------------------------------------------+
bool CRiskManagement::IsMaxTradesReached()
{
    // Count trades opened today
    int todayTrades = 0;
    datetime todayStart = iTime(m_symbol, PERIOD_D1, 0);
    
    HistorySelect(todayStart, TimeCurrent());
    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            if(HistoryDealGetString(ticket, DEAL_SYMBOL) == m_symbol &&
               HistoryDealGetInteger(ticket, DEAL_TIME) >= todayStart &&
               HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_IN)
            {
                todayTrades++;
            }
        }
    }
    
    return (todayTrades >= m_maxTradesPerDay);
}

//+------------------------------------------------------------------+
//| Get maximum risk amount                                          |
//+------------------------------------------------------------------+
double CRiskManagement::GetMaxRiskAmount()
{
    return m_accountBalance * (m_riskPercent / 100.0);
}

//+------------------------------------------------------------------+
//| Get risk amount for specific position                            |
//+------------------------------------------------------------------+
double CRiskManagement::GetRiskAmount(double lotSize, double entryPrice, double stopLoss)
{
    double stopLossDistance = MathAbs(entryPrice - stopLoss);
    double tickValue = GetTickValue();
    double pointValue = GetPointValue();
    
    return lotSize * stopLossDistance * tickValue / pointValue;
}

//+------------------------------------------------------------------+
//| Get potential profit                                             |
//+------------------------------------------------------------------+
double CRiskManagement::GetPotentialProfit(double lotSize, double entryPrice, double takeProfit)
{
    double profitDistance = MathAbs(takeProfit - entryPrice);
    double tickValue = GetTickValue();
    double pointValue = GetPointValue();
    
    return lotSize * profitDistance * tickValue / pointValue;
}

//+------------------------------------------------------------------+
//| Calculate trailing stop                                          |
//+------------------------------------------------------------------+
double CRiskManagement::CalculateTrailingStop(ENUM_ORDER_TYPE orderType, double currentPrice, double atrValue, double multiplier)
{
    double trailingDistance = atrValue * multiplier;
    
    if(orderType == ORDER_TYPE_BUY)
    {
        return currentPrice - trailingDistance;
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        return currentPrice + trailingDistance;
    }
    
    return 0;
}

//+------------------------------------------------------------------+
//| Calculate ATR-based trailing stop                                |
//+------------------------------------------------------------------+
double CRiskManagement::CalculateATRTrailingStop(ENUM_ORDER_TYPE orderType, double currentPrice, int atrPeriod, double multiplier)
{
    int atrHandle = iATR(m_symbol, PERIOD_CURRENT, atrPeriod);
    if(atrHandle == INVALID_HANDLE)
        return 0;
    
    double atrBuffer[];
    if(CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) <= 0)
        return 0;
    
    double atrValue = atrBuffer[0];
    IndicatorRelease(atrHandle);
    
    return CalculateTrailingStop(orderType, currentPrice, atrValue, multiplier);
}

//+------------------------------------------------------------------+
//| Update daily loss                                                |
//+------------------------------------------------------------------+
void CRiskManagement::UpdateDailyLoss(double loss)
{
    if(loss > 0)
        m_currentDailyLoss += loss;
}

//+------------------------------------------------------------------+
//| Update weekly loss                                               |
//+------------------------------------------------------------------+
void CRiskManagement::UpdateWeeklyLoss(double loss)
{
    if(loss > 0)
        m_currentWeeklyLoss += loss;
}

//+------------------------------------------------------------------+
//| Reset daily counters                                             |
//+------------------------------------------------------------------+
void CRiskManagement::ResetDailyCounters()
{
    m_currentDailyLoss = 0;
}

//+------------------------------------------------------------------+
//| Reset weekly counters                                            |
//+------------------------------------------------------------------+
void CRiskManagement::ResetWeeklyCounters()
{
    m_currentWeeklyLoss = 0;
}

//+------------------------------------------------------------------+
//| Update account information                                       |
//+------------------------------------------------------------------+
void CRiskManagement::UpdateAccountInfo()
{
    m_accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    m_accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);
}

//+------------------------------------------------------------------+
//| Update symbol information                                        |
//+------------------------------------------------------------------+
void CRiskManagement::UpdateSymbolInfo()
{
    m_minLotSize = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
    m_maxLotSize = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
    m_lotStep = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
}

//+------------------------------------------------------------------+
//| Get point value                                                  |
//+------------------------------------------------------------------+
double CRiskManagement::GetPointValue()
{
    return SymbolInfoDouble(m_symbol, SYMBOL_POINT);
}

//+------------------------------------------------------------------+
//| Get tick value                                                   |
//+------------------------------------------------------------------+
double CRiskManagement::GetTickValue()
{
    return SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_VALUE);
}

//+------------------------------------------------------------------+
//| Get contract size                                                |
//+------------------------------------------------------------------+
double CRiskManagement::GetContractSize()
{
    return SymbolInfoDouble(m_symbol, SYMBOL_TRADE_CONTRACT_SIZE);
}

#endif // RISK_MANAGEMENT_MQH
