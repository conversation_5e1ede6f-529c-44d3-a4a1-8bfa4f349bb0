//+------------------------------------------------------------------+
//|                                              MarketStructure.mqh |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"

#ifndef MARKET_STRUCTURE_MQH
#define MARKET_STRUCTURE_MQH

//+------------------------------------------------------------------+
//| Market Structure Types                                           |
//+------------------------------------------------------------------+
enum ENUM_STRUCTURE_TYPE
{
    STRUCTURE_NONE,
    STRUCTURE_HIGHER_HIGH,
    STRUCTURE_HIGHER_LOW,
    STRUCTURE_LOWER_HIGH,
    STRUCTURE_LOWER_LOW
};

enum ENUM_TREND_DIRECTION
{
    TREND_NONE,
    TREND_BULLISH,
    TREND_BEARISH,
    TREND_SIDEWAYS
};

//+------------------------------------------------------------------+
//| Structure Point                                                  |
//+------------------------------------------------------------------+
struct SStructurePoint
{
    datetime time;
    double price;
    ENUM_STRUCTURE_TYPE type;
    int barIndex;
    bool isValid;
    bool isBOS;  // Break of Structure
};

//+------------------------------------------------------------------+
//| Market Structure Class                                           |
//+------------------------------------------------------------------+
class CMarketStructure
{
private:
    string m_symbol;
    int m_lookback;
    int m_minSwingSize;
    
    SStructurePoint m_structurePoints[];
    SStructurePoint m_lastHigh;
    SStructurePoint m_lastLow;
    SStructurePoint m_previousHigh;
    SStructurePoint m_previousLow;
    
    ENUM_TREND_DIRECTION m_currentTrend;
    bool m_bosDetected;
    datetime m_lastBOSTime;
    double m_lastBOSPrice;
    ENUM_TREND_DIRECTION m_bosDirection;
    
public:
    CMarketStructure();
    ~CMarketStructure();
    
    bool Initialize(string symbol, int lookback, int minSwingSize);
    void Update();
    
    // Structure detection methods
    bool DetectSwingPoints();
    bool AnalyzeStructure();
    bool DetectBreakOfStructure();
    
    // Getter methods
    ENUM_TREND_DIRECTION GetCurrentTrend() { return m_currentTrend; }
    bool IsBOSDetected() { return m_bosDetected; }
    ENUM_TREND_DIRECTION GetBOSDirection() { return m_bosDirection; }
    datetime GetLastBOSTime() { return m_lastBOSTime; }
    double GetLastBOSPrice() { return m_lastBOSPrice; }
    
    SStructurePoint GetLastHigh() { return m_lastHigh; }
    SStructurePoint GetLastLow() { return m_lastLow; }
    SStructurePoint GetPreviousHigh() { return m_previousHigh; }
    SStructurePoint GetPreviousLow() { return m_previousLow; }
    
    int GetStructurePointsCount() { return ArraySize(m_structurePoints); }
    SStructurePoint GetStructurePoint(int index);
    
    // Utility methods
    bool IsHigherHigh(double currentHigh, double previousHigh);
    bool IsHigherLow(double currentLow, double previousLow);
    bool IsLowerHigh(double currentHigh, double previousHigh);
    bool IsLowerLow(double currentLow, double previousLow);
    
private:
    bool FindSwingHigh(int startBar, int endBar, double &price, int &barIndex);
    bool FindSwingLow(int startBar, int endBar, double &price, int &barIndex);
    void AddStructurePoint(SStructurePoint &point);
    void UpdateTrend();
    double GetPointValue();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CMarketStructure::CMarketStructure()
{
    m_symbol = "";
    m_lookback = 20;
    m_minSwingSize = 5;
    m_currentTrend = TREND_NONE;
    m_bosDetected = false;
    m_lastBOSTime = 0;
    m_lastBOSPrice = 0;
    m_bosDirection = TREND_NONE;
    
    // Initialize structure points
    ZeroMemory(m_lastHigh);
    ZeroMemory(m_lastLow);
    ZeroMemory(m_previousHigh);
    ZeroMemory(m_previousLow);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CMarketStructure::~CMarketStructure()
{
    ArrayFree(m_structurePoints);
}

//+------------------------------------------------------------------+
//| Initialize                                                       |
//+------------------------------------------------------------------+
bool CMarketStructure::Initialize(string symbol, int lookback, int minSwingSize)
{
    m_symbol = symbol;
    m_lookback = lookback;
    m_minSwingSize = minSwingSize;
    
    ArrayResize(m_structurePoints, 0);
    
    return true;
}

//+------------------------------------------------------------------+
//| Update market structure analysis                                 |
//+------------------------------------------------------------------+
void CMarketStructure::Update()
{
    // Detect new swing points
    DetectSwingPoints();
    
    // Analyze structure relationships
    AnalyzeStructure();
    
    // Check for break of structure
    DetectBreakOfStructure();
    
    // Update trend direction
    UpdateTrend();
}

//+------------------------------------------------------------------+
//| Detect swing points                                              |
//+------------------------------------------------------------------+
bool CMarketStructure::DetectSwingPoints()
{
    double swingHigh, swingLow;
    int highBarIndex, lowBarIndex;
    
    // Find swing high
    if(FindSwingHigh(1, m_lookback, swingHigh, highBarIndex))
    {
        // Check if this is a new swing high
        if(m_lastHigh.time != iTime(m_symbol, PERIOD_CURRENT, highBarIndex))
        {
            // Store previous high
            m_previousHigh = m_lastHigh;
            
            // Update last high
            m_lastHigh.time = iTime(m_symbol, PERIOD_CURRENT, highBarIndex);
            m_lastHigh.price = swingHigh;
            m_lastHigh.barIndex = highBarIndex;
            m_lastHigh.isValid = true;
            
            // Determine structure type
            if(m_previousHigh.isValid)
            {
                if(IsHigherHigh(swingHigh, m_previousHigh.price))
                    m_lastHigh.type = STRUCTURE_HIGHER_HIGH;
                else if(IsLowerHigh(swingHigh, m_previousHigh.price))
                    m_lastHigh.type = STRUCTURE_LOWER_HIGH;
            }
            
            AddStructurePoint(m_lastHigh);
        }
    }
    
    // Find swing low
    if(FindSwingLow(1, m_lookback, swingLow, lowBarIndex))
    {
        // Check if this is a new swing low
        if(m_lastLow.time != iTime(m_symbol, PERIOD_CURRENT, lowBarIndex))
        {
            // Store previous low
            m_previousLow = m_lastLow;
            
            // Update last low
            m_lastLow.time = iTime(m_symbol, PERIOD_CURRENT, lowBarIndex);
            m_lastLow.price = swingLow;
            m_lastLow.barIndex = lowBarIndex;
            m_lastLow.isValid = true;
            
            // Determine structure type
            if(m_previousLow.isValid)
            {
                if(IsHigherLow(swingLow, m_previousLow.price))
                    m_lastLow.type = STRUCTURE_HIGHER_LOW;
                else if(IsLowerLow(swingLow, m_previousLow.price))
                    m_lastLow.type = STRUCTURE_LOWER_LOW;
            }
            
            AddStructurePoint(m_lastLow);
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Analyze market structure                                         |
//+------------------------------------------------------------------+
bool CMarketStructure::AnalyzeStructure()
{
    if(!m_lastHigh.isValid || !m_lastLow.isValid || 
       !m_previousHigh.isValid || !m_previousLow.isValid)
        return false;
    
    // Analyze bullish structure (HH + HL)
    if(m_lastHigh.type == STRUCTURE_HIGHER_HIGH && m_lastLow.type == STRUCTURE_HIGHER_LOW)
    {
        m_currentTrend = TREND_BULLISH;
        return true;
    }
    
    // Analyze bearish structure (LH + LL)
    if(m_lastHigh.type == STRUCTURE_LOWER_HIGH && m_lastLow.type == STRUCTURE_LOWER_LOW)
    {
        m_currentTrend = TREND_BEARISH;
        return true;
    }
    
    // Mixed signals - sideways trend
    m_currentTrend = TREND_SIDEWAYS;
    return true;
}

//+------------------------------------------------------------------+
//| Detect break of structure                                        |
//+------------------------------------------------------------------+
bool CMarketStructure::DetectBreakOfStructure()
{
    double currentPrice = SymbolInfoDouble(m_symbol, SYMBOL_BID);
    
    // Reset BOS flag
    m_bosDetected = false;
    
    // Check for bullish BOS (break above previous high)
    if(m_currentTrend == TREND_BEARISH && m_previousHigh.isValid)
    {
        if(currentPrice > m_previousHigh.price)
        {
            m_bosDetected = true;
            m_bosDirection = TREND_BULLISH;
            m_lastBOSTime = TimeCurrent();
            m_lastBOSPrice = m_previousHigh.price;
            return true;
        }
    }
    
    // Check for bearish BOS (break below previous low)
    if(m_currentTrend == TREND_BULLISH && m_previousLow.isValid)
    {
        if(currentPrice < m_previousLow.price)
        {
            m_bosDetected = true;
            m_bosDirection = TREND_BEARISH;
            m_lastBOSTime = TimeCurrent();
            m_lastBOSPrice = m_previousLow.price;
            return true;
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Find swing high                                                  |
//+------------------------------------------------------------------+
bool CMarketStructure::FindSwingHigh(int startBar, int endBar, double &price, int &barIndex)
{
    double highestPrice = 0;
    int highestBar = -1;
    
    for(int i = startBar; i <= endBar; i++)
    {
        double high = iHigh(m_symbol, PERIOD_CURRENT, i);
        
        if(high > highestPrice)
        {
            // Check if this is a valid swing high (higher than surrounding bars)
            bool isSwingHigh = true;
            
            for(int j = 1; j <= 2; j++)
            {
                if(i + j < Bars(m_symbol, PERIOD_CURRENT))
                {
                    if(high <= iHigh(m_symbol, PERIOD_CURRENT, i + j))
                    {
                        isSwingHigh = false;
                        break;
                    }
                }
                
                if(i - j >= 0)
                {
                    if(high <= iHigh(m_symbol, PERIOD_CURRENT, i - j))
                    {
                        isSwingHigh = false;
                        break;
                    }
                }
            }
            
            if(isSwingHigh)
            {
                highestPrice = high;
                highestBar = i;
            }
        }
    }
    
    if(highestBar != -1)
    {
        price = highestPrice;
        barIndex = highestBar;
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Find swing low                                                   |
//+------------------------------------------------------------------+
bool CMarketStructure::FindSwingLow(int startBar, int endBar, double &price, int &barIndex)
{
    double lowestPrice = DBL_MAX;
    int lowestBar = -1;
    
    for(int i = startBar; i <= endBar; i++)
    {
        double low = iLow(m_symbol, PERIOD_CURRENT, i);
        
        if(low < lowestPrice)
        {
            // Check if this is a valid swing low (lower than surrounding bars)
            bool isSwingLow = true;
            
            for(int j = 1; j <= 2; j++)
            {
                if(i + j < Bars(m_symbol, PERIOD_CURRENT))
                {
                    if(low >= iLow(m_symbol, PERIOD_CURRENT, i + j))
                    {
                        isSwingLow = false;
                        break;
                    }
                }
                
                if(i - j >= 0)
                {
                    if(low >= iLow(m_symbol, PERIOD_CURRENT, i - j))
                    {
                        isSwingLow = false;
                        break;
                    }
                }
            }
            
            if(isSwingLow)
            {
                lowestPrice = low;
                lowestBar = i;
            }
        }
    }
    
    if(lowestBar != -1)
    {
        price = lowestPrice;
        barIndex = lowestBar;
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Add structure point to array                                     |
//+------------------------------------------------------------------+
void CMarketStructure::AddStructurePoint(SStructurePoint &point)
{
    int size = ArraySize(m_structurePoints);
    ArrayResize(m_structurePoints, size + 1);
    m_structurePoints[size] = point;
}

//+------------------------------------------------------------------+
//| Update trend direction                                           |
//+------------------------------------------------------------------+
void CMarketStructure::UpdateTrend()
{
    // Trend is updated in AnalyzeStructure method
    // This method can be used for additional trend analysis
}

//+------------------------------------------------------------------+
//| Get structure point by index                                     |
//+------------------------------------------------------------------+
SStructurePoint CMarketStructure::GetStructurePoint(int index)
{
    SStructurePoint emptyPoint;
    ZeroMemory(emptyPoint);
    
    if(index >= 0 && index < ArraySize(m_structurePoints))
        return m_structurePoints[index];
    
    return emptyPoint;
}

//+------------------------------------------------------------------+
//| Check if current high is higher than previous high              |
//+------------------------------------------------------------------+
bool CMarketStructure::IsHigherHigh(double currentHigh, double previousHigh)
{
    return (currentHigh - previousHigh) > (m_minSwingSize * GetPointValue());
}

//+------------------------------------------------------------------+
//| Check if current low is higher than previous low                |
//+------------------------------------------------------------------+
bool CMarketStructure::IsHigherLow(double currentLow, double previousLow)
{
    return (currentLow - previousLow) > (m_minSwingSize * GetPointValue());
}

//+------------------------------------------------------------------+
//| Check if current high is lower than previous high               |
//+------------------------------------------------------------------+
bool CMarketStructure::IsLowerHigh(double currentHigh, double previousHigh)
{
    return (previousHigh - currentHigh) > (m_minSwingSize * GetPointValue());
}

//+------------------------------------------------------------------+
//| Check if current low is lower than previous low                 |
//+------------------------------------------------------------------+
bool CMarketStructure::IsLowerLow(double currentLow, double previousLow)
{
    return (previousLow - currentLow) > (m_minSwingSize * GetPointValue());
}

//+------------------------------------------------------------------+
//| Get point value for the symbol                                   |
//+------------------------------------------------------------------+
double CMarketStructure::GetPointValue()
{
    return SymbolInfoDouble(m_symbol, SYMBOL_POINT);
}

#endif // MARKET_STRUCTURE_MQH
