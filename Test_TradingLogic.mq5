//+------------------------------------------------------------------+
//|                                              Test_TradingLogic.mq5 |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"
#property version   "1.00"

// Test compilation of TradingLogic module
#include "ICT_Modules/TradingLogic.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Test TradingLogic initialization
    CTradingLogic tradingLogic;
    
    if(!tradingLogic.Initialize("EURUSD", 12345))
    {
        Print("Failed to initialize TradingLogic");
        return INIT_FAILED;
    }
    
    Print("TradingLogic module compiled and initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("Test completed");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Test basic functionality
    static bool tested = false;
    if(!tested)
    {
        CTradingLogic tradingLogic;
        tradingLogic.Initialize("EURUSD", 12345);
        
        // Test parameter setting
        tradingLogic.SetParameters(true, true, true, true, true, false);
        
        Print("TradingLogic parameters set successfully");
        
        tested = true;
    }
}
