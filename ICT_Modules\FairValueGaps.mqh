//+------------------------------------------------------------------+
//|                                               FairValueGaps.mqh |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"

#ifndef FAIR_VALUE_GAPS_MQH
#define FAIR_VALUE_GAPS_MQH

//+------------------------------------------------------------------+
//| Fair Value Gap Types                                             |
//+------------------------------------------------------------------+
enum ENUM_FVG_TYPE
{
    FVG_NONE,
    FVG_BULLISH,    // Gap between candle 1 high and candle 3 low
    FVG_BEARISH     // Gap between candle 1 low and candle 3 high
};

enum ENUM_FVG_STATUS
{
    FVG_ACTIVE,
    FVG_PARTIALLY_FILLED,
    FVG_FILLED,
    FVG_EXPIRED
};

//+------------------------------------------------------------------+
//| Fair Value Gap Structure                                         |
//+------------------------------------------------------------------+
struct SFairValueGap
{
    datetime creationTime;
    double upperLevel;
    double lowerLevel;
    double midLevel;
    ENUM_FVG_TYPE type;
    ENUM_FVG_STATUS status;
    int barIndex;
    double gapSize;
    bool isValid;
    datetime lastTestTime;
    int testCount;
    double fillPercentage;
    bool isPartiallyFilled;
    bool isFilled;
    datetime fillTime;
};

//+------------------------------------------------------------------+
//| Fair Value Gaps Class                                            |
//+------------------------------------------------------------------+
class CFairValueGaps
{
private:
    string m_symbol;
    double m_minSize;
    int m_validityBars;
    
    SFairValueGap m_fvgZones[];
    SFairValueGap m_lastBullishFVG;
    SFairValueGap m_lastBearishFVG;
    
public:
    CFairValueGaps();
    ~CFairValueGaps();
    
    bool Initialize(string symbol, double minSize, int validityBars);
    void Update();
    
    // FVG detection methods
    bool DetectFairValueGaps();
    bool DetectBullishFVG(int barIndex);
    bool DetectBearishFVG(int barIndex);
    bool ValidateFVG(SFairValueGap &fvg);
    bool CheckFVGTest();
    bool CheckFVGFill();
    
    // Getter methods
    int GetFVGCount() { return ArraySize(m_fvgZones); }
    SFairValueGap GetFVG(int index);
    SFairValueGap GetLastBullishFVG() { return m_lastBullishFVG; }
    SFairValueGap GetLastBearishFVG() { return m_lastBearishFVG; }
    
    bool HasValidBullishFVG();
    bool HasValidBearishFVG();
    SFairValueGap GetNearestBullishFVG(double currentPrice);
    SFairValueGap GetNearestBearishFVG(double currentPrice);
    
    // Utility methods
    bool IsPriceInFVG(double price, SFairValueGap &fvg);
    double GetFVGFillPercentage(SFairValueGap &fvg, double currentPrice);
    bool IsFVGRejection(SFairValueGap &fvg);
    
private:
    void AddFVG(SFairValueGap &fvg);
    void UpdateFVGStatus();
    void CleanupExpiredFVGs();
    double GetPointValue();
    bool IsGapValid(double gap1, double gap2, double gap3);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFairValueGaps::CFairValueGaps()
{
    m_symbol = "";
    m_minSize = 5.0;
    m_validityBars = 50;
    
    ZeroMemory(m_lastBullishFVG);
    ZeroMemory(m_lastBearishFVG);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CFairValueGaps::~CFairValueGaps()
{
    ArrayFree(m_fvgZones);
}

//+------------------------------------------------------------------+
//| Initialize                                                       |
//+------------------------------------------------------------------+
bool CFairValueGaps::Initialize(string symbol, double minSize, int validityBars)
{
    m_symbol = symbol;
    m_minSize = minSize;
    m_validityBars = validityBars;
    
    ArrayResize(m_fvgZones, 0);
    
    return true;
}

//+------------------------------------------------------------------+
//| Update Fair Value Gaps analysis                                  |
//+------------------------------------------------------------------+
void CFairValueGaps::Update()
{
    // Detect new Fair Value Gaps
    DetectFairValueGaps();
    
    // Check for FVG tests
    CheckFVGTest();
    
    // Check for FVG fills
    CheckFVGFill();
    
    // Update status of existing FVGs
    UpdateFVGStatus();
    
    // Clean up expired FVGs
    CleanupExpiredFVGs();
}

//+------------------------------------------------------------------+
//| Detect Fair Value Gaps                                           |
//+------------------------------------------------------------------+
bool CFairValueGaps::DetectFairValueGaps()
{
    bool found = false;
    
    // Check recent bars for FVG formation (need at least 3 bars)
    for(int i = 3; i <= 20; i++)
    {
        // Check for bullish FVG
        if(DetectBullishFVG(i))
            found = true;
        
        // Check for bearish FVG
        if(DetectBearishFVG(i))
            found = true;
    }
    
    return found;
}

//+------------------------------------------------------------------+
//| Detect Bullish Fair Value Gap                                    |
//+------------------------------------------------------------------+
bool CFairValueGaps::DetectBullishFVG(int barIndex)
{
    // For bullish FVG: candle 1 high < candle 3 low
    // Pattern: [candle 1] [candle 2] [candle 3]
    // Gap between candle 1 high and candle 3 low
    
    double candle1High = iHigh(m_symbol, PERIOD_CURRENT, barIndex);
    double candle1Low = iLow(m_symbol, PERIOD_CURRENT, barIndex);
    double candle2High = iHigh(m_symbol, PERIOD_CURRENT, barIndex - 1);
    double candle2Low = iLow(m_symbol, PERIOD_CURRENT, barIndex - 1);
    double candle3High = iHigh(m_symbol, PERIOD_CURRENT, barIndex - 2);
    double candle3Low = iLow(m_symbol, PERIOD_CURRENT, barIndex - 2);
    
    // Check if there's a gap (candle 1 high < candle 3 low)
    if(candle1High < candle3Low)
    {
        double gapSize = candle3Low - candle1High;
        
        // Check minimum gap size
        if(gapSize >= m_minSize * GetPointValue())
        {
            // Create bullish FVG
            SFairValueGap fvg;
            fvg.creationTime = iTime(m_symbol, PERIOD_CURRENT, barIndex - 2);
            fvg.upperLevel = candle3Low;
            fvg.lowerLevel = candle1High;
            fvg.midLevel = (fvg.upperLevel + fvg.lowerLevel) / 2.0;
            fvg.type = FVG_BULLISH;
            fvg.status = FVG_ACTIVE;
            fvg.barIndex = barIndex - 2;
            fvg.gapSize = gapSize;
            fvg.isValid = true;
            fvg.testCount = 0;
            fvg.fillPercentage = 0.0;
            fvg.isPartiallyFilled = false;
            fvg.isFilled = false;
            
            if(ValidateFVG(fvg))
            {
                // Check if this FVG already exists
                bool exists = false;
                for(int i = 0; i < ArraySize(m_fvgZones); i++)
                {
                    if(m_fvgZones[i].type == FVG_BULLISH &&
                       m_fvgZones[i].creationTime == fvg.creationTime)
                    {
                        exists = true;
                        break;
                    }
                }
                
                if(!exists)
                {
                    AddFVG(fvg);
                    m_lastBullishFVG = fvg;
                    return true;
                }
            }
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Detect Bearish Fair Value Gap                                    |
//+------------------------------------------------------------------+
bool CFairValueGaps::DetectBearishFVG(int barIndex)
{
    // For bearish FVG: candle 1 low > candle 3 high
    // Pattern: [candle 1] [candle 2] [candle 3]
    // Gap between candle 1 low and candle 3 high
    
    double candle1High = iHigh(m_symbol, PERIOD_CURRENT, barIndex);
    double candle1Low = iLow(m_symbol, PERIOD_CURRENT, barIndex);
    double candle2High = iHigh(m_symbol, PERIOD_CURRENT, barIndex - 1);
    double candle2Low = iLow(m_symbol, PERIOD_CURRENT, barIndex - 1);
    double candle3High = iHigh(m_symbol, PERIOD_CURRENT, barIndex - 2);
    double candle3Low = iLow(m_symbol, PERIOD_CURRENT, barIndex - 2);
    
    // Check if there's a gap (candle 1 low > candle 3 high)
    if(candle1Low > candle3High)
    {
        double gapSize = candle1Low - candle3High;
        
        // Check minimum gap size
        if(gapSize >= m_minSize * GetPointValue())
        {
            // Create bearish FVG
            SFairValueGap fvg;
            fvg.creationTime = iTime(m_symbol, PERIOD_CURRENT, barIndex - 2);
            fvg.upperLevel = candle1Low;
            fvg.lowerLevel = candle3High;
            fvg.midLevel = (fvg.upperLevel + fvg.lowerLevel) / 2.0;
            fvg.type = FVG_BEARISH;
            fvg.status = FVG_ACTIVE;
            fvg.barIndex = barIndex - 2;
            fvg.gapSize = gapSize;
            fvg.isValid = true;
            fvg.testCount = 0;
            fvg.fillPercentage = 0.0;
            fvg.isPartiallyFilled = false;
            fvg.isFilled = false;
            
            if(ValidateFVG(fvg))
            {
                // Check if this FVG already exists
                bool exists = false;
                for(int i = 0; i < ArraySize(m_fvgZones); i++)
                {
                    if(m_fvgZones[i].type == FVG_BEARISH &&
                       m_fvgZones[i].creationTime == fvg.creationTime)
                    {
                        exists = true;
                        break;
                    }
                }
                
                if(!exists)
                {
                    AddFVG(fvg);
                    m_lastBearishFVG = fvg;
                    return true;
                }
            }
        }
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Validate Fair Value Gap                                          |
//+------------------------------------------------------------------+
bool CFairValueGaps::ValidateFVG(SFairValueGap &fvg)
{
    // Check minimum gap size
    if(fvg.gapSize < m_minSize * GetPointValue())
        return false;
    
    // Additional validation can be added here
    return true;
}

//+------------------------------------------------------------------+
//| Check for FVG tests                                              |
//+------------------------------------------------------------------+
bool CFairValueGaps::CheckFVGTest()
{
    double currentPrice = SymbolInfoDouble(m_symbol, SYMBOL_BID);
    bool testDetected = false;
    
    for(int i = 0; i < ArraySize(m_fvgZones); i++)
    {
        if(m_fvgZones[i].status == FVG_FILLED || m_fvgZones[i].status == FVG_EXPIRED)
            continue;
        
        // Check if price is testing the FVG
        if(IsPriceInFVG(currentPrice, m_fvgZones[i]))
        {
            m_fvgZones[i].lastTestTime = TimeCurrent();
            m_fvgZones[i].testCount++;
            testDetected = true;
            
            // Calculate fill percentage
            m_fvgZones[i].fillPercentage = GetFVGFillPercentage(m_fvgZones[i], currentPrice);
            
            if(m_fvgZones[i].fillPercentage > 0.5)
            {
                m_fvgZones[i].status = FVG_PARTIALLY_FILLED;
                m_fvgZones[i].isPartiallyFilled = true;
            }
        }
    }
    
    return testDetected;
}

//+------------------------------------------------------------------+
//| Check for FVG fills                                              |
//+------------------------------------------------------------------+
bool CFairValueGaps::CheckFVGFill()
{
    double currentPrice = SymbolInfoDouble(m_symbol, SYMBOL_BID);
    bool fillDetected = false;
    
    for(int i = 0; i < ArraySize(m_fvgZones); i++)
    {
        if(m_fvgZones[i].isFilled)
            continue;
        
        bool isFilled = false;
        
        // Check for bullish FVG fill (price reaches lower level)
        if(m_fvgZones[i].type == FVG_BULLISH)
        {
            if(currentPrice <= m_fvgZones[i].lowerLevel)
                isFilled = true;
        }
        
        // Check for bearish FVG fill (price reaches upper level)
        if(m_fvgZones[i].type == FVG_BEARISH)
        {
            if(currentPrice >= m_fvgZones[i].upperLevel)
                isFilled = true;
        }
        
        if(isFilled)
        {
            m_fvgZones[i].status = FVG_FILLED;
            m_fvgZones[i].isFilled = true;
            m_fvgZones[i].fillTime = TimeCurrent();
            m_fvgZones[i].fillPercentage = 100.0;
            fillDetected = true;
        }
    }
    
    return fillDetected;
}

//+------------------------------------------------------------------+
//| Get FVG by index                                                 |
//+------------------------------------------------------------------+
SFairValueGap CFairValueGaps::GetFVG(int index)
{
    SFairValueGap emptyFVG;
    ZeroMemory(emptyFVG);
    
    if(index >= 0 && index < ArraySize(m_fvgZones))
        return m_fvgZones[index];
    
    return emptyFVG;
}

//+------------------------------------------------------------------+
//| Check if there's a valid bullish FVG                             |
//+------------------------------------------------------------------+
bool CFairValueGaps::HasValidBullishFVG()
{
    for(int i = 0; i < ArraySize(m_fvgZones); i++)
    {
        if(m_fvgZones[i].type == FVG_BULLISH && 
           m_fvgZones[i].status == FVG_ACTIVE &&
           !m_fvgZones[i].isFilled)
            return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if there's a valid bearish FVG                             |
//+------------------------------------------------------------------+
bool CFairValueGaps::HasValidBearishFVG()
{
    for(int i = 0; i < ArraySize(m_fvgZones); i++)
    {
        if(m_fvgZones[i].type == FVG_BEARISH && 
           m_fvgZones[i].status == FVG_ACTIVE &&
           !m_fvgZones[i].isFilled)
            return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get nearest bullish FVG                                          |
//+------------------------------------------------------------------+
SFairValueGap CFairValueGaps::GetNearestBullishFVG(double currentPrice)
{
    SFairValueGap nearestFVG;
    ZeroMemory(nearestFVG);
    double minDistance = DBL_MAX;
    
    for(int i = 0; i < ArraySize(m_fvgZones); i++)
    {
        if(m_fvgZones[i].type == FVG_BULLISH && 
           m_fvgZones[i].status == FVG_ACTIVE &&
           !m_fvgZones[i].isFilled)
        {
            double distance = MathAbs(currentPrice - m_fvgZones[i].midLevel);
            if(distance < minDistance)
            {
                minDistance = distance;
                nearestFVG = m_fvgZones[i];
            }
        }
    }
    
    return nearestFVG;
}

//+------------------------------------------------------------------+
//| Get nearest bearish FVG                                          |
//+------------------------------------------------------------------+
SFairValueGap CFairValueGaps::GetNearestBearishFVG(double currentPrice)
{
    SFairValueGap nearestFVG;
    ZeroMemory(nearestFVG);
    double minDistance = DBL_MAX;
    
    for(int i = 0; i < ArraySize(m_fvgZones); i++)
    {
        if(m_fvgZones[i].type == FVG_BEARISH && 
           m_fvgZones[i].status == FVG_ACTIVE &&
           !m_fvgZones[i].isFilled)
        {
            double distance = MathAbs(currentPrice - m_fvgZones[i].midLevel);
            if(distance < minDistance)
            {
                minDistance = distance;
                nearestFVG = m_fvgZones[i];
            }
        }
    }
    
    return nearestFVG;
}

//+------------------------------------------------------------------+
//| Check if price is in FVG                                         |
//+------------------------------------------------------------------+
bool CFairValueGaps::IsPriceInFVG(double price, SFairValueGap &fvg)
{
    return (price >= fvg.lowerLevel && price <= fvg.upperLevel);
}

//+------------------------------------------------------------------+
//| Get FVG fill percentage                                          |
//+------------------------------------------------------------------+
double CFairValueGaps::GetFVGFillPercentage(SFairValueGap &fvg, double currentPrice)
{
    if(!IsPriceInFVG(currentPrice, fvg))
        return 0.0;
    
    double fillDistance = 0.0;
    
    if(fvg.type == FVG_BULLISH)
    {
        fillDistance = fvg.upperLevel - currentPrice;
    }
    else if(fvg.type == FVG_BEARISH)
    {
        fillDistance = currentPrice - fvg.lowerLevel;
    }
    
    return (fillDistance / fvg.gapSize) * 100.0;
}

//+------------------------------------------------------------------+
//| Check if FVG shows rejection                                     |
//+------------------------------------------------------------------+
bool CFairValueGaps::IsFVGRejection(SFairValueGap &fvg)
{
    // Check if price tested the FVG and rejected from it
    return (fvg.testCount > 0 && fvg.fillPercentage < 80.0);
}

//+------------------------------------------------------------------+
//| Add FVG to array                                                 |
//+------------------------------------------------------------------+
void CFairValueGaps::AddFVG(SFairValueGap &fvg)
{
    int size = ArraySize(m_fvgZones);
    ArrayResize(m_fvgZones, size + 1);
    m_fvgZones[size] = fvg;
}

//+------------------------------------------------------------------+
//| Update FVG status                                                |
//+------------------------------------------------------------------+
void CFairValueGaps::UpdateFVGStatus()
{
    // Status is updated in other methods
}

//+------------------------------------------------------------------+
//| Clean up expired FVGs                                            |
//+------------------------------------------------------------------+
void CFairValueGaps::CleanupExpiredFVGs()
{
    datetime cutoffTime = iTime(m_symbol, PERIOD_CURRENT, m_validityBars);
    
    for(int i = ArraySize(m_fvgZones) - 1; i >= 0; i--)
    {
        if(m_fvgZones[i].creationTime < cutoffTime)
        {
            m_fvgZones[i].status = FVG_EXPIRED;
            
            // Remove expired FVG
            for(int j = i; j < ArraySize(m_fvgZones) - 1; j++)
            {
                m_fvgZones[j] = m_fvgZones[j + 1];
            }
            ArrayResize(m_fvgZones, ArraySize(m_fvgZones) - 1);
        }
    }
}

//+------------------------------------------------------------------+
//| Get point value for the symbol                                   |
//+------------------------------------------------------------------+
double CFairValueGaps::GetPointValue()
{
    return SymbolInfoDouble(m_symbol, SYMBOL_POINT);
}

#endif // FAIR_VALUE_GAPS_MQH
