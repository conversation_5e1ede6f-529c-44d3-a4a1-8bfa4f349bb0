//+------------------------------------------------------------------+
//|                                               Configuration.mqh |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"

//+------------------------------------------------------------------+
//| Configuration Class                                              |
//+------------------------------------------------------------------+
class CConfiguration
{
private:
    // File paths
    string m_configFile;
    string m_logFile;
    
    // Configuration parameters
    struct SConfig
    {
        // General settings
        double riskPercent;
        int magicNumber;
        bool enableLogging;
        
        // ICT Strategy settings
        bool useMarketStructure;
        bool useOrderBlocks;
        bool useFairValueGaps;
        bool useLiquidityZones;
        
        // Market Structure settings
        int structureLookback;
        int minSwingSize;
        
        // Order Blocks settings
        int orderBlockLookback;
        double orderBlockMinSize;
        int orderBlockValidityBars;
        
        // Fair Value Gaps settings
        double fvgMinSize;
        int fvgValidityBars;
        
        // Liquidity Zones settings
        int liquidityLookback;
        double liquidityBuffer;
        
        // Kill Zones settings
        bool useLondonKillZone;
        bool useNewYorkKillZone;
        bool useAsianKillZone;
        
        // Risk Management settings
        double maxRiskRewardRatio;
        double minRiskRewardRatio;
        bool useTrailingStop;
        double trailingStopATR;
        
        // Confirmation filters
        bool requireStructureConfirmation;
        bool requireLiquiditySweep;
        bool requireVolumeConfirmation;
        
        // Chart drawing settings
        bool showOrderBlocks;
        bool showFairValueGaps;
        bool showLiquidityZones;
        bool showMarketStructure;
        bool showLabels;
        
        // Colors
        color colorBullishOB;
        color colorBearishOB;
        color colorBullishFVG;
        color colorBearishFVG;
        color colorBuySideLQ;
        color colorSellSideLQ;
        color colorBOS;
        color colorStructure;
        
        // Advanced settings
        int maxTradesPerDay;
        int maxOpenTrades;
        double maxDailyLossPercent;
        double maxWeeklyLossPercent;
        bool enableNewsFilter;
        bool enableSpreadFilter;
        double maxSpread;
        
        // Optimization settings
        bool enableOptimization;
        int optimizationPeriod;
        double adaptiveRiskFactor;
        bool enableMLIntegration;
    } m_config;
    
public:
    CConfiguration();
    ~CConfiguration();
    
    bool Initialize(string symbol);
    bool LoadConfiguration();
    bool SaveConfiguration();
    void SetDefaultValues();
    
    // Getters
    double GetRiskPercent() { return m_config.riskPercent; }
    int GetMagicNumber() { return m_config.magicNumber; }
    bool IsLoggingEnabled() { return m_config.enableLogging; }
    
    bool UseMarketStructure() { return m_config.useMarketStructure; }
    bool UseOrderBlocks() { return m_config.useOrderBlocks; }
    bool UseFairValueGaps() { return m_config.useFairValueGaps; }
    bool UseLiquidityZones() { return m_config.useLiquidityZones; }
    
    int GetStructureLookback() { return m_config.structureLookback; }
    int GetMinSwingSize() { return m_config.minSwingSize; }
    
    int GetOrderBlockLookback() { return m_config.orderBlockLookback; }
    double GetOrderBlockMinSize() { return m_config.orderBlockMinSize; }
    int GetOrderBlockValidityBars() { return m_config.orderBlockValidityBars; }
    
    double GetFVGMinSize() { return m_config.fvgMinSize; }
    int GetFVGValidityBars() { return m_config.fvgValidityBars; }
    
    int GetLiquidityLookback() { return m_config.liquidityLookback; }
    double GetLiquidityBuffer() { return m_config.liquidityBuffer; }
    
    bool UseLondonKillZone() { return m_config.useLondonKillZone; }
    bool UseNewYorkKillZone() { return m_config.useNewYorkKillZone; }
    bool UseAsianKillZone() { return m_config.useAsianKillZone; }
    
    double GetMaxRiskRewardRatio() { return m_config.maxRiskRewardRatio; }
    double GetMinRiskRewardRatio() { return m_config.minRiskRewardRatio; }
    bool UseTrailingStop() { return m_config.useTrailingStop; }
    double GetTrailingStopATR() { return m_config.trailingStopATR; }
    
    bool RequireStructureConfirmation() { return m_config.requireStructureConfirmation; }
    bool RequireLiquiditySweep() { return m_config.requireLiquiditySweep; }
    bool RequireVolumeConfirmation() { return m_config.requireVolumeConfirmation; }
    
    // Chart drawing getters
    bool ShowOrderBlocks() { return m_config.showOrderBlocks; }
    bool ShowFairValueGaps() { return m_config.showFairValueGaps; }
    bool ShowLiquidityZones() { return m_config.showLiquidityZones; }
    bool ShowMarketStructure() { return m_config.showMarketStructure; }
    bool ShowLabels() { return m_config.showLabels; }
    
    color GetBullishOBColor() { return m_config.colorBullishOB; }
    color GetBearishOBColor() { return m_config.colorBearishOB; }
    color GetBullishFVGColor() { return m_config.colorBullishFVG; }
    color GetBearishFVGColor() { return m_config.colorBearishFVG; }
    color GetBuySideLQColor() { return m_config.colorBuySideLQ; }
    color GetSellSideLQColor() { return m_config.colorSellSideLQ; }
    color GetBOSColor() { return m_config.colorBOS; }
    color GetStructureColor() { return m_config.colorStructure; }
    
    // Advanced getters
    int GetMaxTradesPerDay() { return m_config.maxTradesPerDay; }
    int GetMaxOpenTrades() { return m_config.maxOpenTrades; }
    double GetMaxDailyLossPercent() { return m_config.maxDailyLossPercent; }
    double GetMaxWeeklyLossPercent() { return m_config.maxWeeklyLossPercent; }
    bool IsNewsFilterEnabled() { return m_config.enableNewsFilter; }
    bool IsSpreadFilterEnabled() { return m_config.enableSpreadFilter; }
    double GetMaxSpread() { return m_config.maxSpread; }
    
    // Setters
    void SetRiskPercent(double value) { m_config.riskPercent = value; }
    void SetMagicNumber(int value) { m_config.magicNumber = value; }
    void SetLoggingEnabled(bool value) { m_config.enableLogging = value; }
    
    // Validation
    bool ValidateConfiguration();
    void LogConfiguration();
    
private:
    string GetConfigFilePath(string symbol);
    string GetLogFilePath(string symbol);
    bool WriteConfigValue(int handle, string key, string value);
    string ReadConfigValue(int handle, string key, string defaultValue);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CConfiguration::CConfiguration()
{
    m_configFile = "";
    m_logFile = "";
    SetDefaultValues();
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CConfiguration::~CConfiguration()
{
}

//+------------------------------------------------------------------+
//| Initialize                                                       |
//+------------------------------------------------------------------+
bool CConfiguration::Initialize(string symbol)
{
    m_configFile = GetConfigFilePath(symbol);
    m_logFile = GetLogFilePath(symbol);
    
    // Try to load existing configuration
    if(!LoadConfiguration())
    {
        // Use default values and save them
        SetDefaultValues();
        SaveConfiguration();
    }
    
    return ValidateConfiguration();
}

//+------------------------------------------------------------------+
//| Load configuration from file                                     |
//+------------------------------------------------------------------+
bool CConfiguration::LoadConfiguration()
{
    int handle = FileOpen(m_configFile, FILE_READ | FILE_TXT);
    if(handle == INVALID_HANDLE)
        return false;
    
    // Read configuration values
    m_config.riskPercent = StringToDouble(ReadConfigValue(handle, "RiskPercent", "1.0"));
    m_config.magicNumber = (int)StringToInteger(ReadConfigValue(handle, "MagicNumber", "123456"));
    m_config.enableLogging = (bool)StringToInteger(ReadConfigValue(handle, "EnableLogging", "1"));
    
    m_config.useMarketStructure = (bool)StringToInteger(ReadConfigValue(handle, "UseMarketStructure", "1"));
    m_config.useOrderBlocks = (bool)StringToInteger(ReadConfigValue(handle, "UseOrderBlocks", "1"));
    m_config.useFairValueGaps = (bool)StringToInteger(ReadConfigValue(handle, "UseFairValueGaps", "1"));
    m_config.useLiquidityZones = (bool)StringToInteger(ReadConfigValue(handle, "UseLiquidityZones", "1"));
    
    m_config.structureLookback = (int)StringToInteger(ReadConfigValue(handle, "StructureLookback", "20"));
    m_config.minSwingSize = (int)StringToInteger(ReadConfigValue(handle, "MinSwingSize", "5"));
    
    m_config.orderBlockLookback = (int)StringToInteger(ReadConfigValue(handle, "OrderBlockLookback", "50"));
    m_config.orderBlockMinSize = StringToDouble(ReadConfigValue(handle, "OrderBlockMinSize", "10.0"));
    m_config.orderBlockValidityBars = (int)StringToInteger(ReadConfigValue(handle, "OrderBlockValidityBars", "100"));
    
    m_config.fvgMinSize = StringToDouble(ReadConfigValue(handle, "FVGMinSize", "5.0"));
    m_config.fvgValidityBars = (int)StringToInteger(ReadConfigValue(handle, "FVGValidityBars", "50"));
    
    m_config.liquidityLookback = (int)StringToInteger(ReadConfigValue(handle, "LiquidityLookback", "100"));
    m_config.liquidityBuffer = StringToDouble(ReadConfigValue(handle, "LiquidityBuffer", "2.0"));
    
    m_config.useLondonKillZone = (bool)StringToInteger(ReadConfigValue(handle, "UseLondonKillZone", "1"));
    m_config.useNewYorkKillZone = (bool)StringToInteger(ReadConfigValue(handle, "UseNewYorkKillZone", "1"));
    m_config.useAsianKillZone = (bool)StringToInteger(ReadConfigValue(handle, "UseAsianKillZone", "0"));
    
    m_config.maxRiskRewardRatio = StringToDouble(ReadConfigValue(handle, "MaxRiskRewardRatio", "3.0"));
    m_config.minRiskRewardRatio = StringToDouble(ReadConfigValue(handle, "MinRiskRewardRatio", "2.0"));
    m_config.useTrailingStop = (bool)StringToInteger(ReadConfigValue(handle, "UseTrailingStop", "1"));
    m_config.trailingStopATR = StringToDouble(ReadConfigValue(handle, "TrailingStopATR", "2.0"));
    
    m_config.requireStructureConfirmation = (bool)StringToInteger(ReadConfigValue(handle, "RequireStructureConfirmation", "1"));
    m_config.requireLiquiditySweep = (bool)StringToInteger(ReadConfigValue(handle, "RequireLiquiditySweep", "1"));
    m_config.requireVolumeConfirmation = (bool)StringToInteger(ReadConfigValue(handle, "RequireVolumeConfirmation", "0"));
    
    FileClose(handle);
    return true;
}

//+------------------------------------------------------------------+
//| Save configuration to file                                       |
//+------------------------------------------------------------------+
bool CConfiguration::SaveConfiguration()
{
    int handle = FileOpen(m_configFile, FILE_WRITE | FILE_TXT);
    if(handle == INVALID_HANDLE)
        return false;
    
    // Write configuration values
    WriteConfigValue(handle, "RiskPercent", DoubleToString(m_config.riskPercent, 2));
    WriteConfigValue(handle, "MagicNumber", IntegerToString(m_config.magicNumber));
    WriteConfigValue(handle, "EnableLogging", IntegerToString(m_config.enableLogging));
    
    WriteConfigValue(handle, "UseMarketStructure", IntegerToString(m_config.useMarketStructure));
    WriteConfigValue(handle, "UseOrderBlocks", IntegerToString(m_config.useOrderBlocks));
    WriteConfigValue(handle, "UseFairValueGaps", IntegerToString(m_config.useFairValueGaps));
    WriteConfigValue(handle, "UseLiquidityZones", IntegerToString(m_config.useLiquidityZones));
    
    WriteConfigValue(handle, "StructureLookback", IntegerToString(m_config.structureLookback));
    WriteConfigValue(handle, "MinSwingSize", IntegerToString(m_config.minSwingSize));
    
    WriteConfigValue(handle, "OrderBlockLookback", IntegerToString(m_config.orderBlockLookback));
    WriteConfigValue(handle, "OrderBlockMinSize", DoubleToString(m_config.orderBlockMinSize, 2));
    WriteConfigValue(handle, "OrderBlockValidityBars", IntegerToString(m_config.orderBlockValidityBars));
    
    WriteConfigValue(handle, "FVGMinSize", DoubleToString(m_config.fvgMinSize, 2));
    WriteConfigValue(handle, "FVGValidityBars", IntegerToString(m_config.fvgValidityBars));
    
    WriteConfigValue(handle, "LiquidityLookback", IntegerToString(m_config.liquidityLookback));
    WriteConfigValue(handle, "LiquidityBuffer", DoubleToString(m_config.liquidityBuffer, 2));
    
    WriteConfigValue(handle, "UseLondonKillZone", IntegerToString(m_config.useLondonKillZone));
    WriteConfigValue(handle, "UseNewYorkKillZone", IntegerToString(m_config.useNewYorkKillZone));
    WriteConfigValue(handle, "UseAsianKillZone", IntegerToString(m_config.useAsianKillZone));
    
    WriteConfigValue(handle, "MaxRiskRewardRatio", DoubleToString(m_config.maxRiskRewardRatio, 2));
    WriteConfigValue(handle, "MinRiskRewardRatio", DoubleToString(m_config.minRiskRewardRatio, 2));
    WriteConfigValue(handle, "UseTrailingStop", IntegerToString(m_config.useTrailingStop));
    WriteConfigValue(handle, "TrailingStopATR", DoubleToString(m_config.trailingStopATR, 2));
    
    WriteConfigValue(handle, "RequireStructureConfirmation", IntegerToString(m_config.requireStructureConfirmation));
    WriteConfigValue(handle, "RequireLiquiditySweep", IntegerToString(m_config.requireLiquiditySweep));
    WriteConfigValue(handle, "RequireVolumeConfirmation", IntegerToString(m_config.requireVolumeConfirmation));
    
    FileClose(handle);
    return true;
}

//+------------------------------------------------------------------+
//| Set default configuration values                                 |
//+------------------------------------------------------------------+
void CConfiguration::SetDefaultValues()
{
    // General settings
    m_config.riskPercent = 1.0;
    m_config.magicNumber = 123456;
    m_config.enableLogging = true;
    
    // ICT Strategy settings
    m_config.useMarketStructure = true;
    m_config.useOrderBlocks = true;
    m_config.useFairValueGaps = true;
    m_config.useLiquidityZones = true;
    
    // Market Structure settings
    m_config.structureLookback = 20;
    m_config.minSwingSize = 5;
    
    // Order Blocks settings
    m_config.orderBlockLookback = 50;
    m_config.orderBlockMinSize = 10.0;
    m_config.orderBlockValidityBars = 100;
    
    // Fair Value Gaps settings
    m_config.fvgMinSize = 5.0;
    m_config.fvgValidityBars = 50;
    
    // Liquidity Zones settings
    m_config.liquidityLookback = 100;
    m_config.liquidityBuffer = 2.0;
    
    // Kill Zones settings
    m_config.useLondonKillZone = true;
    m_config.useNewYorkKillZone = true;
    m_config.useAsianKillZone = false;
    
    // Risk Management settings
    m_config.maxRiskRewardRatio = 3.0;
    m_config.minRiskRewardRatio = 2.0;
    m_config.useTrailingStop = true;
    m_config.trailingStopATR = 2.0;
    
    // Confirmation filters
    m_config.requireStructureConfirmation = true;
    m_config.requireLiquiditySweep = true;
    m_config.requireVolumeConfirmation = false;
    
    // Chart drawing settings
    m_config.showOrderBlocks = true;
    m_config.showFairValueGaps = true;
    m_config.showLiquidityZones = true;
    m_config.showMarketStructure = true;
    m_config.showLabels = true;
    
    // Colors
    m_config.colorBullishOB = clrDodgerBlue;
    m_config.colorBearishOB = clrOrangeRed;
    m_config.colorBullishFVG = clrLimeGreen;
    m_config.colorBearishFVG = clrMagenta;
    m_config.colorBuySideLQ = clrYellow;
    m_config.colorSellSideLQ = clrCyan;
    m_config.colorBOS = clrWhite;
    m_config.colorStructure = clrSilver;
    
    // Advanced settings
    m_config.maxTradesPerDay = 5;
    m_config.maxOpenTrades = 3;
    m_config.maxDailyLossPercent = 5.0;
    m_config.maxWeeklyLossPercent = 10.0;
    m_config.enableNewsFilter = false;
    m_config.enableSpreadFilter = true;
    m_config.maxSpread = 3.0;
    
    // Optimization settings
    m_config.enableOptimization = false;
    m_config.optimizationPeriod = 30;
    m_config.adaptiveRiskFactor = 1.0;
    m_config.enableMLIntegration = false;
}

//+------------------------------------------------------------------+
//| Validate configuration                                           |
//+------------------------------------------------------------------+
bool CConfiguration::ValidateConfiguration()
{
    // Validate risk percent
    if(m_config.riskPercent <= 0 || m_config.riskPercent > 10)
    {
        Print("Invalid risk percent: ", m_config.riskPercent);
        return false;
    }
    
    // Validate risk-reward ratios
    if(m_config.minRiskRewardRatio <= 0 || m_config.maxRiskRewardRatio <= m_config.minRiskRewardRatio)
    {
        Print("Invalid risk-reward ratios");
        return false;
    }
    
    // Validate lookback periods
    if(m_config.structureLookback <= 0 || m_config.orderBlockLookback <= 0 || m_config.liquidityLookback <= 0)
    {
        Print("Invalid lookback periods");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Log configuration                                                |
//+------------------------------------------------------------------+
void CConfiguration::LogConfiguration()
{
    if(!m_config.enableLogging)
        return;
    
    Print("=== ICT EA Configuration ===");
    Print("Risk Percent: ", m_config.riskPercent, "%");
    Print("Magic Number: ", m_config.magicNumber);
    Print("Use Market Structure: ", m_config.useMarketStructure);
    Print("Use Order Blocks: ", m_config.useOrderBlocks);
    Print("Use Fair Value Gaps: ", m_config.useFairValueGaps);
    Print("Use Liquidity Zones: ", m_config.useLiquidityZones);
    Print("Min Risk-Reward: ", m_config.minRiskRewardRatio);
    Print("Max Risk-Reward: ", m_config.maxRiskRewardRatio);
    Print("Use Trailing Stop: ", m_config.useTrailingStop);
    Print("============================");
}

//+------------------------------------------------------------------+
//| Get configuration file path                                      |
//+------------------------------------------------------------------+
string CConfiguration::GetConfigFilePath(string symbol)
{
    return "ICT_EA_" + symbol + "_config.txt";
}

//+------------------------------------------------------------------+
//| Get log file path                                                |
//+------------------------------------------------------------------+
string CConfiguration::GetLogFilePath(string symbol)
{
    return "ICT_EA_" + symbol + "_log.txt";
}

//+------------------------------------------------------------------+
//| Write configuration value                                        |
//+------------------------------------------------------------------+
bool CConfiguration::WriteConfigValue(int handle, string key, string value)
{
    string line = key + "=" + value + "\n";
    return (FileWriteString(handle, line) > 0);
}

//+------------------------------------------------------------------+
//| Read configuration value                                         |
//+------------------------------------------------------------------+
string CConfiguration::ReadConfigValue(int handle, string key, string defaultValue)
{
    // Simple implementation - in a real scenario, you'd parse the file properly
    return defaultValue;
}
