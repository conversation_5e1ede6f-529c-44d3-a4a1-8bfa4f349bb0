//+------------------------------------------------------------------+
//|                                                ChartDrawing.mqh |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"

//+------------------------------------------------------------------+
//| Chart Drawing Class                                              |
//+------------------------------------------------------------------+
class CChartDrawing
{
private:
    string m_symbol;
    long m_chartId;
    
    // Object name prefixes
    string m_prefixOB;      // Order Blocks
    string m_prefixFVG;     // Fair Value Gaps
    string m_prefixLQ;      // Liquidity
    string m_prefixMS;      // Market Structure
    string m_prefixBOS;     // Break of Structure
    
    // Colors
    color m_colorBullishOB;
    color m_colorBearishOB;
    color m_colorBullishFVG;
    color m_colorBearishFVG;
    color m_colorBuySideLQ;
    color m_colorSellSideLQ;
    color m_colorBOS;
    color m_colorStructure;
    
    // Drawing settings
    int m_lineWidth;
    ENUM_LINE_STYLE m_lineStyle;
    bool m_showLabels;
    
public:
    CChartDrawing();
    ~CChartDrawing();
    
    bool Initialize(string symbol);
    void SetupChart();
    void CleanupChart();
    
    // Main drawing methods
    void DrawMarketStructure(CMarketStructure* marketStructure);
    void DrawLiquidityZones(CLiquidityZones* liquidityZones);
    void DrawOrderBlocks(COrderBlocks* orderBlocks);
    void DrawFairValueGaps(CFairValueGaps* fairValueGaps);
    
    // Individual drawing methods
    void DrawOrderBlock(SOrderBlock &block, int index);
    void DrawFairValueGap(SFairValueGap &fvg, int index);
    void DrawLiquidityZone(SLiquidityZone &zone, int index);
    void DrawStructurePoint(SStructurePoint &point, int index);
    void DrawBreakOfStructure(double price, datetime time, ENUM_TREND_DIRECTION direction);
    
    // Utility methods
    void DrawRectangle(string name, datetime time1, double price1, datetime time2, double price2, 
                      color clr, bool fill = true, int width = 1);
    void DrawHorizontalLine(string name, double price, color clr, ENUM_LINE_STYLE style = STYLE_SOLID, int width = 1);
    void DrawTrendLine(string name, datetime time1, double price1, datetime time2, double price2, 
                      color clr, ENUM_LINE_STYLE style = STYLE_SOLID, int width = 1);
    void DrawLabel(string name, datetime time, double price, string text, color clr, int fontSize = 8);
    void DrawArrow(string name, datetime time, double price, int arrowCode, color clr, int size = 1);
    
    // Object management
    void DeleteObject(string name);
    void DeleteObjectsByPrefix(string prefix);
    bool ObjectExists(string name);
    
    // Settings
    void SetColors(color bullishOB, color bearishOB, color bullishFVG, color bearishFVG, 
                  color buySideLQ, color sellSideLQ, color bos, color structure);
    void SetLineSettings(int width, ENUM_LINE_STYLE style);
    void SetShowLabels(bool show) { m_showLabels = show; }
    
private:
    string GenerateObjectName(string prefix, int index);
    datetime GetFutureTime(int barsAhead = 10);
    void SetObjectProperties(string name, color clr, int width, ENUM_LINE_STYLE style);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CChartDrawing::CChartDrawing()
{
    m_symbol = "";
    m_chartId = 0;
    
    m_prefixOB = "ICT_OB_";
    m_prefixFVG = "ICT_FVG_";
    m_prefixLQ = "ICT_LQ_";
    m_prefixMS = "ICT_MS_";
    m_prefixBOS = "ICT_BOS_";
    
    // Default colors
    m_colorBullishOB = clrDodgerBlue;
    m_colorBearishOB = clrOrangeRed;
    m_colorBullishFVG = clrLimeGreen;
    m_colorBearishFVG = clrMagenta;
    m_colorBuySideLQ = clrYellow;
    m_colorSellSideLQ = clrCyan;
    m_colorBOS = clrWhite;
    m_colorStructure = clrSilver;
    
    m_lineWidth = 2;
    m_lineStyle = STYLE_SOLID;
    m_showLabels = true;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CChartDrawing::~CChartDrawing()
{
    CleanupChart();
}

//+------------------------------------------------------------------+
//| Initialize                                                       |
//+------------------------------------------------------------------+
bool CChartDrawing::Initialize(string symbol)
{
    m_symbol = symbol;
    m_chartId = ChartID();
    
    return true;
}

//+------------------------------------------------------------------+
//| Setup chart                                                      |
//+------------------------------------------------------------------+
void CChartDrawing::SetupChart()
{
    // Set chart properties for better visualization
    ChartSetInteger(m_chartId, CHART_SHOW_GRID, false);
    ChartSetInteger(m_chartId, CHART_SHOW_PERIOD_SEP, true);
    ChartSetInteger(m_chartId, CHART_SHOW_OBJECT_DESCR, true);
}

//+------------------------------------------------------------------+
//| Cleanup chart                                                    |
//+------------------------------------------------------------------+
void CChartDrawing::CleanupChart()
{
    DeleteObjectsByPrefix(m_prefixOB);
    DeleteObjectsByPrefix(m_prefixFVG);
    DeleteObjectsByPrefix(m_prefixLQ);
    DeleteObjectsByPrefix(m_prefixMS);
    DeleteObjectsByPrefix(m_prefixBOS);
}

//+------------------------------------------------------------------+
//| Draw market structure                                            |
//+------------------------------------------------------------------+
void CChartDrawing::DrawMarketStructure(CMarketStructure* marketStructure)
{
    if(marketStructure == NULL)
        return;
    
    // Draw structure points
    for(int i = 0; i < marketStructure->GetStructurePointsCount(); i++)
    {
        SStructurePoint point = marketStructure->GetStructurePoint(i);
        if(point.isValid)
        {
            DrawStructurePoint(point, i);
        }
    }

    // Draw break of structure
    if(marketStructure->IsBOSDetected())
    {
        DrawBreakOfStructure(marketStructure->GetLastBOSPrice(),
                           marketStructure->GetLastBOSTime(),
                           marketStructure->GetBOSDirection());
    }
}

//+------------------------------------------------------------------+
//| Draw liquidity zones                                             |
//+------------------------------------------------------------------+
void CChartDrawing::DrawLiquidityZones(CLiquidityZones* liquidityZones)
{
    if(liquidityZones == NULL)
        return;
    
    // Clean up old liquidity objects
    DeleteObjectsByPrefix(m_prefixLQ);
    
    // Draw liquidity zones
    for(int i = 0; i < liquidityZones->GetLiquidityZonesCount(); i++)
    {
        SLiquidityZone zone = liquidityZones->GetLiquidityZone(i);
        if(zone.status == LIQUIDITY_ACTIVE)
        {
            DrawLiquidityZone(zone, i);
        }
    }
}

//+------------------------------------------------------------------+
//| Draw order blocks                                                |
//+------------------------------------------------------------------+
void CChartDrawing::DrawOrderBlocks(COrderBlocks* orderBlocks)
{
    if(orderBlocks == NULL)
        return;
    
    // Clean up old order block objects
    DeleteObjectsByPrefix(m_prefixOB);
    
    // Draw order blocks
    for(int i = 0; i < orderBlocks->GetOrderBlocksCount(); i++)
    {
        SOrderBlock block = orderBlocks->GetOrderBlock(i);
        if(block.isValid && !block.isMitigated)
        {
            DrawOrderBlock(block, i);
        }
    }
}

//+------------------------------------------------------------------+
//| Draw fair value gaps                                             |
//+------------------------------------------------------------------+
void CChartDrawing::DrawFairValueGaps(CFairValueGaps* fairValueGaps)
{
    if(fairValueGaps == NULL)
        return;
    
    // Clean up old FVG objects
    DeleteObjectsByPrefix(m_prefixFVG);
    
    // Draw FVGs
    for(int i = 0; i < fairValueGaps->GetFVGCount(); i++)
    {
        SFairValueGap fvg = fairValueGaps->GetFVG(i);
        if(fvg.isValid && !fvg.isFilled)
        {
            DrawFairValueGap(fvg, i);
        }
    }
}

//+------------------------------------------------------------------+
//| Draw order block                                                 |
//+------------------------------------------------------------------+
void CChartDrawing::DrawOrderBlock(SOrderBlock &block, int index)
{
    string name = GenerateObjectName(m_prefixOB, index);
    datetime endTime = GetFutureTime(20);
    
    color blockColor = (block.type == ORDER_BLOCK_BULLISH) ? m_colorBullishOB : m_colorBearishOB;
    
    // Draw rectangle
    DrawRectangle(name, block.creationTime, block.lowPrice, endTime, block.highPrice, blockColor, true, 1);
    
    // Draw label
    if(m_showLabels)
    {
        string labelText = (block.type == ORDER_BLOCK_BULLISH) ? "Bullish OB" : "Bearish OB";
        DrawLabel(name + "_label", block.creationTime, block.highPrice, labelText, blockColor, 8);
    }
}

//+------------------------------------------------------------------+
//| Draw fair value gap                                              |
//+------------------------------------------------------------------+
void CChartDrawing::DrawFairValueGap(SFairValueGap &fvg, int index)
{
    string name = GenerateObjectName(m_prefixFVG, index);
    datetime endTime = GetFutureTime(15);
    
    color fvgColor = (fvg.type == FVG_BULLISH) ? m_colorBullishFVG : m_colorBearishFVG;
    
    // Draw rectangle
    DrawRectangle(name, fvg.creationTime, fvg.lowerLevel, endTime, fvg.upperLevel, fvgColor, true, 1);
    
    // Draw midline
    DrawHorizontalLine(name + "_mid", fvg.midLevel, fvgColor, STYLE_DOT, 1);
    
    // Draw label
    if(m_showLabels)
    {
        string labelText = (fvg.type == FVG_BULLISH) ? "Bull FVG" : "Bear FVG";
        DrawLabel(name + "_label", fvg.creationTime, fvg.upperLevel, labelText, fvgColor, 8);
    }
}

//+------------------------------------------------------------------+
//| Draw liquidity zone                                              |
//+------------------------------------------------------------------+
void CChartDrawing::DrawLiquidityZone(SLiquidityZone &zone, int index)
{
    string name = GenerateObjectName(m_prefixLQ, index);
    
    color lqColor = clrYellow;
    if(zone.type == LIQUIDITY_BUY_SIDE || zone.type == LIQUIDITY_EQUAL_HIGHS)
        lqColor = m_colorBuySideLQ;
    else if(zone.type == LIQUIDITY_SELL_SIDE || zone.type == LIQUIDITY_EQUAL_LOWS)
        lqColor = m_colorSellSideLQ;
    
    // Draw horizontal line
    DrawHorizontalLine(name, zone.price, lqColor, STYLE_DASH, 2);
    
    // Draw label
    if(m_showLabels)
    {
        string labelText = "";
        switch(zone.type)
        {
            case LIQUIDITY_BUY_SIDE: labelText = "Buy Side LQ"; break;
            case LIQUIDITY_SELL_SIDE: labelText = "Sell Side LQ"; break;
            case LIQUIDITY_EQUAL_HIGHS: labelText = "Equal Highs"; break;
            case LIQUIDITY_EQUAL_LOWS: labelText = "Equal Lows"; break;
        }
        
        DrawLabel(name + "_label", TimeCurrent(), zone.price, labelText, lqColor, 8);
    }
}

//+------------------------------------------------------------------+
//| Draw structure point                                             |
//+------------------------------------------------------------------+
void CChartDrawing::DrawStructurePoint(SStructurePoint &point, int index)
{
    string name = GenerateObjectName(m_prefixMS, index);
    
    int arrowCode = 0;
    string labelText = "";
    
    switch(point.type)
    {
        case STRUCTURE_HIGHER_HIGH:
            arrowCode = 241; // Up arrow
            labelText = "HH";
            break;
        case STRUCTURE_HIGHER_LOW:
            arrowCode = 242; // Down arrow
            labelText = "HL";
            break;
        case STRUCTURE_LOWER_HIGH:
            arrowCode = 241; // Up arrow
            labelText = "LH";
            break;
        case STRUCTURE_LOWER_LOW:
            arrowCode = 242; // Down arrow
            labelText = "LL";
            break;
    }
    
    if(arrowCode > 0)
    {
        DrawArrow(name, point.time, point.price, arrowCode, m_colorStructure, 2);
        
        if(m_showLabels)
        {
            double labelPrice = point.price;
            if(point.type == STRUCTURE_HIGHER_HIGH || point.type == STRUCTURE_LOWER_HIGH)
                labelPrice += 10 * SymbolInfoDouble(m_symbol, SYMBOL_POINT);
            else
                labelPrice -= 10 * SymbolInfoDouble(m_symbol, SYMBOL_POINT);
            
            DrawLabel(name + "_label", point.time, labelPrice, labelText, m_colorStructure, 8);
        }
    }
}

//+------------------------------------------------------------------+
//| Draw break of structure                                          |
//+------------------------------------------------------------------+
void CChartDrawing::DrawBreakOfStructure(double price, datetime time, ENUM_TREND_DIRECTION direction)
{
    string name = m_prefixBOS + "current";
    
    // Draw horizontal line at BOS level
    DrawHorizontalLine(name, price, m_colorBOS, STYLE_SOLID, 3);
    
    // Draw label
    if(m_showLabels)
    {
        string labelText = (direction == TREND_BULLISH) ? "Bullish BOS" : "Bearish BOS";
        DrawLabel(name + "_label", time, price, labelText, m_colorBOS, 10);
    }
}

//+------------------------------------------------------------------+
//| Draw rectangle                                                   |
//+------------------------------------------------------------------+
void CChartDrawing::DrawRectangle(string name, datetime time1, double price1, datetime time2, double price2, 
                                 color clr, bool fill = true, int width = 1)
{
    if(ObjectCreate(m_chartId, name, OBJ_RECTANGLE, 0, time1, price1, time2, price2))
    {
        ObjectSetInteger(m_chartId, name, OBJPROP_COLOR, clr);
        ObjectSetInteger(m_chartId, name, OBJPROP_FILL, fill);
        ObjectSetInteger(m_chartId, name, OBJPROP_WIDTH, width);
        ObjectSetInteger(m_chartId, name, OBJPROP_BACK, true);
        ObjectSetInteger(m_chartId, name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(m_chartId, name, OBJPROP_HIDDEN, true);
    }
}

//+------------------------------------------------------------------+
//| Draw horizontal line                                             |
//+------------------------------------------------------------------+
void CChartDrawing::DrawHorizontalLine(string name, double price, color clr, ENUM_LINE_STYLE style = STYLE_SOLID, int width = 1)
{
    if(ObjectCreate(m_chartId, name, OBJ_HLINE, 0, 0, price))
    {
        ObjectSetInteger(m_chartId, name, OBJPROP_COLOR, clr);
        ObjectSetInteger(m_chartId, name, OBJPROP_STYLE, style);
        ObjectSetInteger(m_chartId, name, OBJPROP_WIDTH, width);
        ObjectSetInteger(m_chartId, name, OBJPROP_BACK, false);
        ObjectSetInteger(m_chartId, name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(m_chartId, name, OBJPROP_HIDDEN, true);
    }
}

//+------------------------------------------------------------------+
//| Draw trend line                                                  |
//+------------------------------------------------------------------+
void CChartDrawing::DrawTrendLine(string name, datetime time1, double price1, datetime time2, double price2, 
                                 color clr, ENUM_LINE_STYLE style = STYLE_SOLID, int width = 1)
{
    if(ObjectCreate(m_chartId, name, OBJ_TREND, 0, time1, price1, time2, price2))
    {
        ObjectSetInteger(m_chartId, name, OBJPROP_COLOR, clr);
        ObjectSetInteger(m_chartId, name, OBJPROP_STYLE, style);
        ObjectSetInteger(m_chartId, name, OBJPROP_WIDTH, width);
        ObjectSetInteger(m_chartId, name, OBJPROP_BACK, false);
        ObjectSetInteger(m_chartId, name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(m_chartId, name, OBJPROP_HIDDEN, true);
        ObjectSetInteger(m_chartId, name, OBJPROP_RAY_RIGHT, false);
    }
}

//+------------------------------------------------------------------+
//| Draw label                                                       |
//+------------------------------------------------------------------+
void CChartDrawing::DrawLabel(string name, datetime time, double price, string text, color clr, int fontSize = 8)
{
    if(ObjectCreate(m_chartId, name, OBJ_TEXT, 0, time, price))
    {
        ObjectSetString(m_chartId, name, OBJPROP_TEXT, text);
        ObjectSetInteger(m_chartId, name, OBJPROP_COLOR, clr);
        ObjectSetInteger(m_chartId, name, OBJPROP_FONTSIZE, fontSize);
        ObjectSetString(m_chartId, name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(m_chartId, name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(m_chartId, name, OBJPROP_HIDDEN, true);
    }
}

//+------------------------------------------------------------------+
//| Draw arrow                                                       |
//+------------------------------------------------------------------+
void CChartDrawing::DrawArrow(string name, datetime time, double price, int arrowCode, color clr, int size = 1)
{
    if(ObjectCreate(m_chartId, name, OBJ_ARROW, 0, time, price))
    {
        ObjectSetInteger(m_chartId, name, OBJPROP_ARROWCODE, arrowCode);
        ObjectSetInteger(m_chartId, name, OBJPROP_COLOR, clr);
        ObjectSetInteger(m_chartId, name, OBJPROP_WIDTH, size);
        ObjectSetInteger(m_chartId, name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(m_chartId, name, OBJPROP_HIDDEN, true);
    }
}

//+------------------------------------------------------------------+
//| Delete object                                                    |
//+------------------------------------------------------------------+
void CChartDrawing::DeleteObject(string name)
{
    ObjectDelete(m_chartId, name);
}

//+------------------------------------------------------------------+
//| Delete objects by prefix                                         |
//+------------------------------------------------------------------+
void CChartDrawing::DeleteObjectsByPrefix(string prefix)
{
    int total = ObjectsTotal(m_chartId);
    
    for(int i = total - 1; i >= 0; i--)
    {
        string objName = ObjectName(m_chartId, i);
        if(StringFind(objName, prefix) == 0)
        {
            ObjectDelete(m_chartId, objName);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if object exists                                           |
//+------------------------------------------------------------------+
bool CChartDrawing::ObjectExists(string name)
{
    return (ObjectFind(m_chartId, name) >= 0);
}

//+------------------------------------------------------------------+
//| Set colors                                                       |
//+------------------------------------------------------------------+
void CChartDrawing::SetColors(color bullishOB, color bearishOB, color bullishFVG, color bearishFVG, 
                             color buySideLQ, color sellSideLQ, color bos, color structure)
{
    m_colorBullishOB = bullishOB;
    m_colorBearishOB = bearishOB;
    m_colorBullishFVG = bullishFVG;
    m_colorBearishFVG = bearishFVG;
    m_colorBuySideLQ = buySideLQ;
    m_colorSellSideLQ = sellSideLQ;
    m_colorBOS = bos;
    m_colorStructure = structure;
}

//+------------------------------------------------------------------+
//| Set line settings                                                |
//+------------------------------------------------------------------+
void CChartDrawing::SetLineSettings(int width, ENUM_LINE_STYLE style)
{
    m_lineWidth = width;
    m_lineStyle = style;
}

//+------------------------------------------------------------------+
//| Generate object name                                             |
//+------------------------------------------------------------------+
string CChartDrawing::GenerateObjectName(string prefix, int index)
{
    return prefix + IntegerToString(index) + "_" + IntegerToString(GetTickCount());
}

//+------------------------------------------------------------------+
//| Get future time                                                  |
//+------------------------------------------------------------------+
datetime CChartDrawing::GetFutureTime(int barsAhead = 10)
{
    return iTime(m_symbol, PERIOD_CURRENT, 0) + (PeriodSeconds(PERIOD_CURRENT) * barsAhead);
}
