//+------------------------------------------------------------------+
//|                                           ICT_Expert_Advisor.mq5 |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Professional ICT (Inner Circle Trader) Expert Advisor"
#property description "Features: Market Structure, Liquidity Zones, Order Blocks, Fair Value Gaps"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>
#include <Trade\AccountInfo.mqh>
#include <Trade\SymbolInfo.mqh>

//--- Include custom modules
#include "ICT_Modules\MarketStructure.mqh"
#include "ICT_Modules\LiquidityZones.mqh"
#include "ICT_Modules\OrderBlocks.mqh"
#include "ICT_Modules\FairValueGaps.mqh"
#include "ICT_Modules\RiskManagement.mqh"
#include "ICT_Modules\ChartDrawing.mqh"
#include "ICT_Modules\TradingLogic.mqh"

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== GENERAL SETTINGS ==="
input double   RiskPercent = 1.0;              // Risk % per trade
input int      MagicNumber = 123456;           // Magic number for trades
input bool     EnableLogging = true;           // Enable detailed logging

input group "=== ICT STRATEGY SETTINGS ==="
input bool     UseMarketStructure = true;      // Use Market Structure analysis
input bool     UseOrderBlocks = true;          // Use Order Blocks
input bool     UseFairValueGaps = true;        // Use Fair Value Gaps
input bool     UseLiquidityZones = true;       // Use Liquidity Zones

input group "=== MARKET STRUCTURE ==="
input int      StructureLookback = 20;         // Bars to look back for structure
input int      MinSwingSize = 5;               // Minimum swing size in points

input group "=== ORDER BLOCKS ==="
input int      OrderBlockLookback = 50;        // Bars to look back for order blocks
input double   OrderBlockMinSize = 10.0;       // Minimum order block size in points
input int      OrderBlockValidityBars = 100;   // Order block validity in bars

input group "=== FAIR VALUE GAPS ==="
input double   FVGMinSize = 5.0;               // Minimum FVG size in points
input int      FVGValidityBars = 50;           // FVG validity in bars

input group "=== LIQUIDITY ZONES ==="
input int      LiquidityLookback = 100;        // Bars to look back for liquidity
input double   LiquidityBuffer = 2.0;          // Buffer around liquidity levels in points

input group "=== KILL ZONES ==="
input bool     UseLondonKillZone = true;       // Use London Kill Zone (2-5 AM GMT)
input bool     UseNewYorkKillZone = true;      // Use New York Kill Zone (7-10 AM EST)
input bool     UseAsianKillZone = false;       // Use Asian Kill Zone (12-3 AM GMT)

input group "=== RISK MANAGEMENT ==="
input double   MaxRiskRewardRatio = 3.0;       // Maximum Risk:Reward ratio
input double   MinRiskRewardRatio = 2.0;       // Minimum Risk:Reward ratio
input bool     UseTrailingStop = true;         // Use trailing stop
input double   TrailingStopATR = 2.0;          // ATR multiplier for trailing stop

input group "=== CONFIRMATION FILTERS ==="
input bool     RequireStructureConfirmation = true;  // Require structure confirmation
input bool     RequireLiquiditySweep = true;         // Require liquidity sweep
input bool     RequireVolumeConfirmation = false;    // Require volume confirmation

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade         trade;
CPositionInfo  position;
COrderInfo     order;
CAccountInfo   account;
CSymbolInfo    symbol;

// ICT Module instances
CMarketStructure    *marketStructure;
CLiquidityZones     *liquidityZones;
COrderBlocks        *orderBlocks;
CFairValueGaps      *fairValueGaps;
CRiskManagement     *riskManager;
CChartDrawing       *chartDrawing;
CTradingLogic       *tradingLogic;

// Global variables
datetime lastBarTime = 0;
bool isNewBar = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize symbol info
    if(!symbol.Name(_Symbol))
    {
        Print("ERROR: Failed to initialize symbol info");
        return INIT_FAILED;
    }
    
    // Set magic number for trade operations
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(_Symbol);
    
    // Initialize ICT modules
    marketStructure = new CMarketStructure();
    liquidityZones = new CLiquidityZones();
    orderBlocks = new COrderBlocks();
    fairValueGaps = new CFairValueGaps();
    riskManager = new CRiskManagement();
    chartDrawing = new CChartDrawing();
    tradingLogic = new CTradingLogic();
    
    // Initialize modules with parameters
    if(!InitializeModules())
    {
        Print("ERROR: Failed to initialize ICT modules");
        return INIT_FAILED;
    }
    
    // Set up chart drawing
    chartDrawing.SetupChart();
    
    Print("ICT Expert Advisor initialized successfully");
    Print("Symbol: ", _Symbol);
    Print("Risk per trade: ", RiskPercent, "%");
    Print("Magic Number: ", MagicNumber);
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Clean up chart objects
    if(chartDrawing != NULL)
        chartDrawing.CleanupChart();
    
    // Delete module instances
    if(marketStructure != NULL) delete marketStructure;
    if(liquidityZones != NULL) delete liquidityZones;
    if(orderBlocks != NULL) delete orderBlocks;
    if(fairValueGaps != NULL) delete fairValueGaps;
    if(riskManager != NULL) delete riskManager;
    if(chartDrawing != NULL) delete chartDrawing;
    if(tradingLogic != NULL) delete tradingLogic;
    
    Print("ICT Expert Advisor deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check for new bar
    CheckNewBar();
    
    // Update symbol info
    if(!symbol.RefreshRates())
        return;
    
    // Process on new bar only
    if(isNewBar)
    {
        ProcessNewBar();
    }
    
    // Check for trade management on every tick
    ManageOpenTrades();
}

//+------------------------------------------------------------------+
//| Check for new bar                                               |
//+------------------------------------------------------------------+
void CheckNewBar()
{
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(currentBarTime != lastBarTime)
    {
        isNewBar = true;
        lastBarTime = currentBarTime;
    }
    else
    {
        isNewBar = false;
    }
}

//+------------------------------------------------------------------+
//| Process new bar                                                  |
//+------------------------------------------------------------------+
void ProcessNewBar()
{
    // Update all ICT modules
    UpdateModules();
    
    // Check for trading opportunities
    CheckTradingOpportunities();
    
    // Update chart drawings
    UpdateChartDrawings();
}

//+------------------------------------------------------------------+
//| Initialize all ICT modules                                       |
//+------------------------------------------------------------------+
bool InitializeModules()
{
    // Initialize Market Structure module
    if(!marketStructure.Initialize(_Symbol, StructureLookback, MinSwingSize))
        return false;
    
    // Initialize Liquidity Zones module
    if(!liquidityZones.Initialize(_Symbol, LiquidityLookback, LiquidityBuffer))
        return false;
    
    // Initialize Order Blocks module
    if(!orderBlocks.Initialize(_Symbol, OrderBlockLookback, OrderBlockMinSize, OrderBlockValidityBars))
        return false;
    
    // Initialize Fair Value Gaps module
    if(!fairValueGaps.Initialize(_Symbol, FVGMinSize, FVGValidityBars))
        return false;
    
    // Initialize Risk Management module
    if(!riskManager.Initialize(_Symbol, RiskPercent, MinRiskRewardRatio, MaxRiskRewardRatio))
        return false;
    
    // Initialize Chart Drawing module
    if(!chartDrawing.Initialize(_Symbol))
        return false;
    
    // Initialize Trading Logic module
    if(!tradingLogic.Initialize(_Symbol, MagicNumber))
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Update all ICT modules                                           |
//+------------------------------------------------------------------+
void UpdateModules()
{
    if(UseMarketStructure && marketStructure != NULL)
        marketStructure.Update();
    
    if(UseLiquidityZones && liquidityZones != NULL)
        liquidityZones.Update();
    
    if(UseOrderBlocks && orderBlocks != NULL)
        orderBlocks.Update();
    
    if(UseFairValueGaps && fairValueGaps != NULL)
        fairValueGaps.Update();
}

//+------------------------------------------------------------------+
//| Check for trading opportunities                                  |
//+------------------------------------------------------------------+
void CheckTradingOpportunities()
{
    // Check if we're in a kill zone
    if(!IsInKillZone())
        return;

    // Set trading logic parameters
    if(tradingLogic != NULL)
    {
        tradingLogic.SetParameters(UseMarketStructure, UseOrderBlocks, UseFairValueGaps,
                                  UseLiquidityZones, RequireStructureConfirmation, RequireLiquiditySweep);

        // Check for trading signals
        tradingLogic.CheckForSignals(marketStructure, liquidityZones, orderBlocks, fairValueGaps);

        // Execute signal if valid
        STradingSignal signal = tradingLogic.GetLastSignal();
        if(signal.isValid && signal.signalTime > iTime(_Symbol, PERIOD_CURRENT, 1))
        {
            tradingLogic.ExecuteSignal(signal, riskManager);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if current time is in kill zone                           |
//+------------------------------------------------------------------+
bool IsInKillZone()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    int currentHour = dt.hour;
    
    // London Kill Zone: 2-5 AM GMT
    if(UseLondonKillZone && currentHour >= 2 && currentHour < 5)
        return true;
    
    // New York Kill Zone: 7-10 AM EST (12-15 GMT)
    if(UseNewYorkKillZone && currentHour >= 12 && currentHour < 15)
        return true;
    
    // Asian Kill Zone: 12-3 AM GMT
    if(UseAsianKillZone && currentHour >= 0 && currentHour < 3)
        return true;
    
    return false;
}

//+------------------------------------------------------------------+
//| Manage open trades                                               |
//+------------------------------------------------------------------+
void ManageOpenTrades()
{
    if(tradingLogic != NULL)
    {
        tradingLogic.ManageOpenTrades(UseTrailingStop, TrailingStopATR);
    }
}

//+------------------------------------------------------------------+
//| Update chart drawings                                            |
//+------------------------------------------------------------------+
void UpdateChartDrawings()
{
    if(chartDrawing != NULL)
    {
        chartDrawing.DrawMarketStructure(marketStructure);
        chartDrawing.DrawLiquidityZones(liquidityZones);
        chartDrawing.DrawOrderBlocks(orderBlocks);
        chartDrawing.DrawFairValueGaps(fairValueGaps);
    }
}
