# ICT Expert Advisor for MetaTrader 5

A professional Expert Advisor implementing the Inner Circle Trader (ICT) methodology for automated trading on MetaTrader 5.

## Features

### Core ICT Concepts
- **Market Structure Analysis**: Detects Higher Highs (HH), Higher Lows (HL), Lower Highs (LH), Lower Lows (LL)
- **Break of Structure (BOS)**: Identifies trend changes and smart money direction
- **Order Blocks**: Detects bullish and bearish order blocks with retracement entries
- **Fair Value Gaps (FVG)**: Identifies imbalance zones and rejection patterns
- **Liquidity Zones**: Maps buy-side and sell-side liquidity areas

### Trading Features
- **Kill Zones**: London (2-5 AM GMT), New York (7-10 AM EST), Asian (12-3 AM GMT)
- **Smart Entry Logic**: Multiple confirmation filters for high-probability setups
- **Dynamic Risk Management**: Position sizing based on account balance and risk percentage
- **Trailing Stops**: ATR-based trailing stop functionality
- **Risk-Reward Optimization**: Configurable 1:2 to 1:3 risk-reward ratios

### Visual Elements
- **Chart Drawing**: Automatic visualization of all ICT concepts
- **Color-Coded Zones**: Different colors for bullish/bearish patterns
- **Real-Time Updates**: Dynamic updating of zones and levels
- **Clean Interface**: Professional chart presentation

## Installation

1. Copy all files to your MetaTrader 5 `MQL5/Experts/` directory
2. Ensure the `ICT_Modules/` folder is in the same directory as the main EA file
3. Compile the EA in MetaEditor
4. Attach to your desired chart

## File Structure

```
ICT_Expert_Advisor.mq5          # Main EA file
ICT_Modules/
├── MarketStructure.mqh         # Market structure detection
├── LiquidityZones.mqh          # Liquidity analysis
├── OrderBlocks.mqh             # Order block detection
├── FairValueGaps.mqh           # FVG identification
├── RiskManagement.mqh          # Risk and money management
├── TradingLogic.mqh            # Entry/exit logic
├── ChartDrawing.mqh            # Visual elements
└── Configuration.mqh           # Settings management
```

## Configuration Parameters

### General Settings
- **Risk Percent**: Risk per trade (1-2% recommended)
- **Magic Number**: Unique identifier for EA trades
- **Enable Logging**: Detailed logging for analysis

### ICT Strategy Settings
- **Use Market Structure**: Enable/disable structure analysis
- **Use Order Blocks**: Enable/disable order block detection
- **Use Fair Value Gaps**: Enable/disable FVG analysis
- **Use Liquidity Zones**: Enable/disable liquidity mapping

### Market Structure
- **Structure Lookback**: Bars to analyze for structure (default: 20)
- **Min Swing Size**: Minimum swing size in points (default: 5)

### Order Blocks
- **Lookback Period**: Bars to scan for order blocks (default: 50)
- **Minimum Size**: Minimum order block size in points (default: 10)
- **Validity Period**: How long order blocks remain valid (default: 100 bars)

### Fair Value Gaps
- **Minimum Size**: Minimum FVG size in points (default: 5)
- **Validity Period**: How long FVGs remain active (default: 50 bars)

### Liquidity Zones
- **Lookback Period**: Bars to analyze for liquidity (default: 100)
- **Buffer Size**: Buffer around liquidity levels in points (default: 2)

### Kill Zones
- **London Kill Zone**: 2-5 AM GMT (recommended: enabled)
- **New York Kill Zone**: 7-10 AM EST (recommended: enabled)
- **Asian Kill Zone**: 12-3 AM GMT (optional)

### Risk Management
- **Max Risk-Reward**: Maximum RR ratio (default: 3.0)
- **Min Risk-Reward**: Minimum RR ratio (default: 2.0)
- **Use Trailing Stop**: Enable ATR-based trailing stops
- **Trailing ATR Multiplier**: ATR multiplier for trailing stops (default: 2.0)

### Confirmation Filters
- **Require Structure Confirmation**: Must have bullish/bearish structure
- **Require Liquidity Sweep**: Must have recent liquidity sweep
- **Require Volume Confirmation**: Volume-based confirmation (optional)

## Trading Logic

### Entry Conditions

#### Buy Signal Requirements:
1. **Market Structure**: Bullish structure (HH + HL) or bullish BOS
2. **Liquidity Sweep**: Recent sell-side liquidity sweep (optional)
3. **Entry Trigger**: Price retracement to:
   - Bullish Order Block, OR
   - Bullish Fair Value Gap
4. **Confirmation**: Rejection pattern or engulfing candle
5. **Kill Zone**: Must be within active trading session

#### Sell Signal Requirements:
1. **Market Structure**: Bearish structure (LH + LL) or bearish BOS
2. **Liquidity Sweep**: Recent buy-side liquidity sweep (optional)
3. **Entry Trigger**: Price retracement to:
   - Bearish Order Block, OR
   - Bearish Fair Value Gap
4. **Confirmation**: Rejection pattern or engulfing candle
5. **Kill Zone**: Must be within active trading session

### Exit Strategy

#### Stop Loss Placement:
- **Buy Trades**: Below recent swing low or sell-side liquidity
- **Sell Trades**: Above recent swing high or buy-side liquidity
- **Buffer**: 5-point buffer beyond key levels

#### Take Profit:
- **Risk-Reward**: 1:2 to 1:3 ratio based on stop loss distance
- **Structure Targets**: Major resistance/support levels
- **Partial Profits**: Optional scaling out at key levels

#### Trailing Stops:
- **ATR-Based**: Dynamic trailing based on Average True Range
- **Structure-Based**: Trail to break-even after structure confirmation

## Risk Management

### Position Sizing
- **Fixed Risk**: Percentage of account balance per trade
- **Dynamic Calculation**: Lot size based on stop loss distance
- **Maximum Exposure**: Limits on concurrent positions

### Account Protection
- **Daily Loss Limit**: Maximum daily loss percentage (default: 5%)
- **Weekly Loss Limit**: Maximum weekly loss percentage (default: 10%)
- **Maximum Trades**: Daily trade limit (default: 5 trades)
- **Drawdown Protection**: Automatic trading halt on excessive losses

## Performance Optimization

### VPS Compatibility
- **Optimized Code**: Efficient algorithms for VPS environments
- **Memory Management**: Proper cleanup and resource management
- **Error Handling**: Comprehensive error checking and recovery

### Broker Compatibility
- **Universal Design**: Works with any MT5 broker
- **Execution Modes**: Supports all order execution types
- **Spread Filtering**: Optional spread-based trade filtering

## Future Enhancements

### Machine Learning Integration
- **Pattern Recognition**: AI-based pattern identification
- **Adaptive Parameters**: Self-optimizing parameters
- **Market Regime Detection**: Automatic strategy adjustment

### Advanced Features
- **News Filter**: Economic news-based trade filtering
- **Correlation Analysis**: Multi-timeframe correlation
- **Sentiment Analysis**: Market sentiment integration
- **Portfolio Management**: Multi-symbol trading

## Support and Updates

### Documentation
- **User Manual**: Comprehensive trading guide
- **Video Tutorials**: Step-by-step setup instructions
- **Strategy Guide**: ICT methodology explanation

### Maintenance
- **Regular Updates**: Bug fixes and improvements
- **New Features**: Continuous development
- **Community Support**: User forum and feedback

## Disclaimer

This Expert Advisor is for educational and research purposes. Trading involves substantial risk of loss. Past performance does not guarantee future results. Always test on demo accounts before live trading.

## License

Copyright 2025, ICT Trader EA. All rights reserved.

## Contact

For support, updates, and additional resources, please visit our website or contact our support team.

---

**Version**: 1.00  
**Release Date**: January 2025  
**Compatibility**: MetaTrader 5 Build 3815+  
**Minimum Deposit**: $1000 (recommended)  
**Recommended Timeframes**: M15, H1, H4  
**Recommended Pairs**: Major forex pairs (EURUSD, GBPUSD, USDJPY, etc.)
