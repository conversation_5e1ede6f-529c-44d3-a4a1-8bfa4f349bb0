//+------------------------------------------------------------------+
//|                                                  OrderBlocks.mqh |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"

#ifndef ORDER_BLOCKS_MQH
#define ORDER_BLOCKS_MQH

//+------------------------------------------------------------------+
//| Order Block Types                                                |
//+------------------------------------------------------------------+
enum ENUM_ORDER_BLOCK_TYPE
{
    ORDER_BLOCK_NONE,
    ORDER_BLOCK_BULLISH,
    ORDER_BLOCK_BEARISH
};

enum ENUM_ORDER_BLOCK_STATUS
{
    ORDER_BLOCK_ACTIVE,
    ORDER_BLOCK_TESTED,
    ORDER_BLOCK_MITIGATED,
    ORDER_BLOCK_EXPIRED
};

//+------------------------------------------------------------------+
//| Order Block Structure                                            |
//+------------------------------------------------------------------+
struct SOrderBlock
{
    datetime creationTime;
    double highPrice;
    double lowPrice;
    double openPrice;
    double closePrice;
    ENUM_ORDER_BLOCK_TYPE type;
    ENUM_ORDER_BLOCK_STATUS status;
    int barIndex;
    int strength;           // Based on volume, size, etc.
    bool isValid;
    datetime lastTestTime;
    int testCount;
    bool isMitigated;
    datetime mitigationTime;
    double mitigationPrice;
};

//+------------------------------------------------------------------+
//| Order Blocks Class                                               |
//+------------------------------------------------------------------+
class COrderBlocks
{
private:
    string m_symbol;
    int m_lookback;
    double m_minSize;
    int m_validityBars;
    
    SOrderBlock m_orderBlocks[];
    SOrderBlock m_lastBullishOB;
    SOrderBlock m_lastBearishOB;
    
public:
    COrderBlocks();
    ~COrderBlocks();
    
    bool Initialize(string symbol, int lookback, double minSize, int validityBars);
    void Update();
    
    // Order block detection methods
    bool DetectBullishOrderBlocks();
    bool DetectBearishOrderBlocks();
    bool ValidateOrderBlock(SOrderBlock &block);
    bool CheckOrderBlockTest();
    bool CheckOrderBlockMitigation();
    
    // Getter methods
    int GetOrderBlocksCount() { return ArraySize(m_orderBlocks); }
    SOrderBlock GetOrderBlock(int index);
    SOrderBlock GetLastBullishOrderBlock() { return m_lastBullishOB; }
    SOrderBlock GetLastBearishOrderBlock() { return m_lastBearishOB; }
    
    bool HasValidBullishOrderBlock();
    bool HasValidBearishOrderBlock();
    SOrderBlock GetNearestBullishOrderBlock(double currentPrice);
    SOrderBlock GetNearestBearishOrderBlock(double currentPrice);
    
    // Utility methods
    bool IsPriceInOrderBlock(double price, SOrderBlock &block);
    double GetOrderBlockStrength(SOrderBlock &block);
    bool IsEngulfingPattern(int barIndex);
    
private:
    void AddOrderBlock(SOrderBlock &block);
    void UpdateOrderBlockStatus();
    void CleanupExpiredBlocks();
    bool IsStrongBearishCandle(int barIndex);
    bool IsStrongBullishCandle(int barIndex);
    double GetCandleSize(int barIndex);
    double GetPointValue();
    bool HasSignificantVolume(int barIndex);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
COrderBlocks::COrderBlocks()
{
    m_symbol = "";
    m_lookback = 50;
    m_minSize = 10.0;
    m_validityBars = 100;
    
    ZeroMemory(m_lastBullishOB);
    ZeroMemory(m_lastBearishOB);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
COrderBlocks::~COrderBlocks()
{
    ArrayFree(m_orderBlocks);
}

//+------------------------------------------------------------------+
//| Initialize                                                       |
//+------------------------------------------------------------------+
bool COrderBlocks::Initialize(string symbol, int lookback, double minSize, int validityBars)
{
    m_symbol = symbol;
    m_lookback = lookback;
    m_minSize = minSize;
    m_validityBars = validityBars;
    
    ArrayResize(m_orderBlocks, 0);
    
    return true;
}

//+------------------------------------------------------------------+
//| Update order blocks analysis                                     |
//+------------------------------------------------------------------+
void COrderBlocks::Update()
{
    // Detect new bullish order blocks
    DetectBullishOrderBlocks();
    
    // Detect new bearish order blocks
    DetectBearishOrderBlocks();
    
    // Check for order block tests
    CheckOrderBlockTest();
    
    // Check for order block mitigation
    CheckOrderBlockMitigation();
    
    // Update status of existing blocks
    UpdateOrderBlockStatus();
    
    // Clean up expired blocks
    CleanupExpiredBlocks();
}

//+------------------------------------------------------------------+
//| Detect bullish order blocks                                      |
//+------------------------------------------------------------------+
bool COrderBlocks::DetectBullishOrderBlocks()
{
    bool found = false;
    
    // Look for bullish order blocks in recent bars
    for(int i = 2; i <= m_lookback; i++)
    {
        // Check for strong bearish candle followed by bullish movement
        if(IsStrongBearishCandle(i))
        {
            // Check if price moved up significantly after this candle
            double candleLow = iLow(m_symbol, PERIOD_CURRENT, i);
            double currentPrice = SymbolInfoDouble(m_symbol, SYMBOL_BID);
            
            // Look for bullish movement in subsequent candles
            bool bullishMovement = false;
            for(int j = i - 1; j >= 1; j--)
            {
                if(iClose(m_symbol, PERIOD_CURRENT, j) > iHigh(m_symbol, PERIOD_CURRENT, i))
                {
                    bullishMovement = true;
                    break;
                }
            }
            
            if(bullishMovement)
            {
                // Create bullish order block
                SOrderBlock block;
                block.creationTime = iTime(m_symbol, PERIOD_CURRENT, i);
                block.highPrice = iHigh(m_symbol, PERIOD_CURRENT, i);
                block.lowPrice = iLow(m_symbol, PERIOD_CURRENT, i);
                block.openPrice = iOpen(m_symbol, PERIOD_CURRENT, i);
                block.closePrice = iClose(m_symbol, PERIOD_CURRENT, i);
                block.type = ORDER_BLOCK_BULLISH;
                block.status = ORDER_BLOCK_ACTIVE;
                block.barIndex = i;
                block.isValid = true;
                block.testCount = 0;
                block.isMitigated = false;
                
                // Calculate strength
                block.strength = (int)(GetCandleSize(i) / GetPointValue());
                
                if(ValidateOrderBlock(block))
                {
                    // Check if this block already exists
                    bool exists = false;
                    for(int k = 0; k < ArraySize(m_orderBlocks); k++)
                    {
                        if(m_orderBlocks[k].type == ORDER_BLOCK_BULLISH &&
                           m_orderBlocks[k].creationTime == block.creationTime)
                        {
                            exists = true;
                            break;
                        }
                    }
                    
                    if(!exists)
                    {
                        AddOrderBlock(block);
                        m_lastBullishOB = block;
                        found = true;
                    }
                }
            }
        }
    }
    
    return found;
}

//+------------------------------------------------------------------+
//| Detect bearish order blocks                                      |
//+------------------------------------------------------------------+
bool COrderBlocks::DetectBearishOrderBlocks()
{
    bool found = false;
    
    // Look for bearish order blocks in recent bars
    for(int i = 2; i <= m_lookback; i++)
    {
        // Check for strong bullish candle followed by bearish movement
        if(IsStrongBullishCandle(i))
        {
            // Check if price moved down significantly after this candle
            double candleHigh = iHigh(m_symbol, PERIOD_CURRENT, i);
            double currentPrice = SymbolInfoDouble(m_symbol, SYMBOL_BID);
            
            // Look for bearish movement in subsequent candles
            bool bearishMovement = false;
            for(int j = i - 1; j >= 1; j--)
            {
                if(iClose(m_symbol, PERIOD_CURRENT, j) < iLow(m_symbol, PERIOD_CURRENT, i))
                {
                    bearishMovement = true;
                    break;
                }
            }
            
            if(bearishMovement)
            {
                // Create bearish order block
                SOrderBlock block;
                block.creationTime = iTime(m_symbol, PERIOD_CURRENT, i);
                block.highPrice = iHigh(m_symbol, PERIOD_CURRENT, i);
                block.lowPrice = iLow(m_symbol, PERIOD_CURRENT, i);
                block.openPrice = iOpen(m_symbol, PERIOD_CURRENT, i);
                block.closePrice = iClose(m_symbol, PERIOD_CURRENT, i);
                block.type = ORDER_BLOCK_BEARISH;
                block.status = ORDER_BLOCK_ACTIVE;
                block.barIndex = i;
                block.isValid = true;
                block.testCount = 0;
                block.isMitigated = false;
                
                // Calculate strength
                block.strength = (int)(GetCandleSize(i) / GetPointValue());
                
                if(ValidateOrderBlock(block))
                {
                    // Check if this block already exists
                    bool exists = false;
                    for(int k = 0; k < ArraySize(m_orderBlocks); k++)
                    {
                        if(m_orderBlocks[k].type == ORDER_BLOCK_BEARISH &&
                           m_orderBlocks[k].creationTime == block.creationTime)
                        {
                            exists = true;
                            break;
                        }
                    }
                    
                    if(!exists)
                    {
                        AddOrderBlock(block);
                        m_lastBearishOB = block;
                        found = true;
                    }
                }
            }
        }
    }
    
    return found;
}

//+------------------------------------------------------------------+
//| Validate order block                                             |
//+------------------------------------------------------------------+
bool COrderBlocks::ValidateOrderBlock(SOrderBlock &block)
{
    // Check minimum size requirement
    double blockSize = block.highPrice - block.lowPrice;
    if(blockSize < m_minSize * GetPointValue())
        return false;
    
    // Check if block has significant volume (if available)
    if(!HasSignificantVolume(block.barIndex))
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check for order block tests                                      |
//+------------------------------------------------------------------+
bool COrderBlocks::CheckOrderBlockTest()
{
    double currentPrice = SymbolInfoDouble(m_symbol, SYMBOL_BID);
    bool testDetected = false;
    
    for(int i = 0; i < ArraySize(m_orderBlocks); i++)
    {
        if(m_orderBlocks[i].status != ORDER_BLOCK_ACTIVE)
            continue;
        
        // Check if price is testing the order block
        if(IsPriceInOrderBlock(currentPrice, m_orderBlocks[i]))
        {
            m_orderBlocks[i].status = ORDER_BLOCK_TESTED;
            m_orderBlocks[i].lastTestTime = TimeCurrent();
            m_orderBlocks[i].testCount++;
            testDetected = true;
        }
    }
    
    return testDetected;
}

//+------------------------------------------------------------------+
//| Check for order block mitigation                                 |
//+------------------------------------------------------------------+
bool COrderBlocks::CheckOrderBlockMitigation()
{
    double currentPrice = SymbolInfoDouble(m_symbol, SYMBOL_BID);
    bool mitigationDetected = false;
    
    for(int i = 0; i < ArraySize(m_orderBlocks); i++)
    {
        if(m_orderBlocks[i].isMitigated)
            continue;
        
        // Check for bullish order block mitigation (price closes below the low)
        if(m_orderBlocks[i].type == ORDER_BLOCK_BULLISH)
        {
            if(currentPrice < m_orderBlocks[i].lowPrice)
            {
                m_orderBlocks[i].status = ORDER_BLOCK_MITIGATED;
                m_orderBlocks[i].isMitigated = true;
                m_orderBlocks[i].mitigationTime = TimeCurrent();
                m_orderBlocks[i].mitigationPrice = currentPrice;
                mitigationDetected = true;
            }
        }
        
        // Check for bearish order block mitigation (price closes above the high)
        if(m_orderBlocks[i].type == ORDER_BLOCK_BEARISH)
        {
            if(currentPrice > m_orderBlocks[i].highPrice)
            {
                m_orderBlocks[i].status = ORDER_BLOCK_MITIGATED;
                m_orderBlocks[i].isMitigated = true;
                m_orderBlocks[i].mitigationTime = TimeCurrent();
                m_orderBlocks[i].mitigationPrice = currentPrice;
                mitigationDetected = true;
            }
        }
    }
    
    return mitigationDetected;
}

//+------------------------------------------------------------------+
//| Get order block by index                                         |
//+------------------------------------------------------------------+
SOrderBlock COrderBlocks::GetOrderBlock(int index)
{
    SOrderBlock emptyBlock;
    ZeroMemory(emptyBlock);
    
    if(index >= 0 && index < ArraySize(m_orderBlocks))
        return m_orderBlocks[index];
    
    return emptyBlock;
}

//+------------------------------------------------------------------+
//| Check if there's a valid bullish order block                     |
//+------------------------------------------------------------------+
bool COrderBlocks::HasValidBullishOrderBlock()
{
    for(int i = 0; i < ArraySize(m_orderBlocks); i++)
    {
        if(m_orderBlocks[i].type == ORDER_BLOCK_BULLISH && 
           m_orderBlocks[i].status == ORDER_BLOCK_ACTIVE &&
           !m_orderBlocks[i].isMitigated)
            return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if there's a valid bearish order block                     |
//+------------------------------------------------------------------+
bool COrderBlocks::HasValidBearishOrderBlock()
{
    for(int i = 0; i < ArraySize(m_orderBlocks); i++)
    {
        if(m_orderBlocks[i].type == ORDER_BLOCK_BEARISH && 
           m_orderBlocks[i].status == ORDER_BLOCK_ACTIVE &&
           !m_orderBlocks[i].isMitigated)
            return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get nearest bullish order block                                  |
//+------------------------------------------------------------------+
SOrderBlock COrderBlocks::GetNearestBullishOrderBlock(double currentPrice)
{
    SOrderBlock nearestBlock;
    ZeroMemory(nearestBlock);
    double minDistance = DBL_MAX;
    
    for(int i = 0; i < ArraySize(m_orderBlocks); i++)
    {
        if(m_orderBlocks[i].type == ORDER_BLOCK_BULLISH && 
           m_orderBlocks[i].status == ORDER_BLOCK_ACTIVE &&
           !m_orderBlocks[i].isMitigated)
        {
            double distance = MathAbs(currentPrice - m_orderBlocks[i].lowPrice);
            if(distance < minDistance)
            {
                minDistance = distance;
                nearestBlock = m_orderBlocks[i];
            }
        }
    }
    
    return nearestBlock;
}

//+------------------------------------------------------------------+
//| Get nearest bearish order block                                  |
//+------------------------------------------------------------------+
SOrderBlock COrderBlocks::GetNearestBearishOrderBlock(double currentPrice)
{
    SOrderBlock nearestBlock;
    ZeroMemory(nearestBlock);
    double minDistance = DBL_MAX;
    
    for(int i = 0; i < ArraySize(m_orderBlocks); i++)
    {
        if(m_orderBlocks[i].type == ORDER_BLOCK_BEARISH && 
           m_orderBlocks[i].status == ORDER_BLOCK_ACTIVE &&
           !m_orderBlocks[i].isMitigated)
        {
            double distance = MathAbs(currentPrice - m_orderBlocks[i].highPrice);
            if(distance < minDistance)
            {
                minDistance = distance;
                nearestBlock = m_orderBlocks[i];
            }
        }
    }
    
    return nearestBlock;
}

//+------------------------------------------------------------------+
//| Check if price is in order block                                 |
//+------------------------------------------------------------------+
bool COrderBlocks::IsPriceInOrderBlock(double price, SOrderBlock &block)
{
    return (price >= block.lowPrice && price <= block.highPrice);
}

//+------------------------------------------------------------------+
//| Add order block to array                                         |
//+------------------------------------------------------------------+
void COrderBlocks::AddOrderBlock(SOrderBlock &block)
{
    int size = ArraySize(m_orderBlocks);
    ArrayResize(m_orderBlocks, size + 1);
    m_orderBlocks[size] = block;
}

//+------------------------------------------------------------------+
//| Update order block status                                        |
//+------------------------------------------------------------------+
void COrderBlocks::UpdateOrderBlockStatus()
{
    // Status is updated in other methods
}

//+------------------------------------------------------------------+
//| Clean up expired blocks                                          |
//+------------------------------------------------------------------+
void COrderBlocks::CleanupExpiredBlocks()
{
    datetime cutoffTime = iTime(m_symbol, PERIOD_CURRENT, m_validityBars);
    
    for(int i = ArraySize(m_orderBlocks) - 1; i >= 0; i--)
    {
        if(m_orderBlocks[i].creationTime < cutoffTime)
        {
            m_orderBlocks[i].status = ORDER_BLOCK_EXPIRED;
            
            // Remove expired block
            for(int j = i; j < ArraySize(m_orderBlocks) - 1; j++)
            {
                m_orderBlocks[j] = m_orderBlocks[j + 1];
            }
            ArrayResize(m_orderBlocks, ArraySize(m_orderBlocks) - 1);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if candle is strong bearish                                |
//+------------------------------------------------------------------+
bool COrderBlocks::IsStrongBearishCandle(int barIndex)
{
    double open = iOpen(m_symbol, PERIOD_CURRENT, barIndex);
    double close = iClose(m_symbol, PERIOD_CURRENT, barIndex);
    double high = iHigh(m_symbol, PERIOD_CURRENT, barIndex);
    double low = iLow(m_symbol, PERIOD_CURRENT, barIndex);
    
    // Check if it's a bearish candle
    if(close >= open)
        return false;
    
    // Check if body is significant portion of the range
    double bodySize = open - close;
    double totalRange = high - low;
    
    return (bodySize / totalRange) > 0.6;
}

//+------------------------------------------------------------------+
//| Check if candle is strong bullish                                |
//+------------------------------------------------------------------+
bool COrderBlocks::IsStrongBullishCandle(int barIndex)
{
    double open = iOpen(m_symbol, PERIOD_CURRENT, barIndex);
    double close = iClose(m_symbol, PERIOD_CURRENT, barIndex);
    double high = iHigh(m_symbol, PERIOD_CURRENT, barIndex);
    double low = iLow(m_symbol, PERIOD_CURRENT, barIndex);
    
    // Check if it's a bullish candle
    if(close <= open)
        return false;
    
    // Check if body is significant portion of the range
    double bodySize = close - open;
    double totalRange = high - low;
    
    return (bodySize / totalRange) > 0.6;
}

//+------------------------------------------------------------------+
//| Get candle size                                                   |
//+------------------------------------------------------------------+
double COrderBlocks::GetCandleSize(int barIndex)
{
    double high = iHigh(m_symbol, PERIOD_CURRENT, barIndex);
    double low = iLow(m_symbol, PERIOD_CURRENT, barIndex);
    return high - low;
}

//+------------------------------------------------------------------+
//| Get point value for the symbol                                   |
//+------------------------------------------------------------------+
double COrderBlocks::GetPointValue()
{
    return SymbolInfoDouble(m_symbol, SYMBOL_POINT);
}

//+------------------------------------------------------------------+
//| Check if bar has significant volume                              |
//+------------------------------------------------------------------+
bool COrderBlocks::HasSignificantVolume(int barIndex)
{
    // Volume analysis can be implemented here
    // For now, return true as volume data might not be available for all symbols
    return true;
}

#endif // ORDER_BLOCKS_MQH
