//+------------------------------------------------------------------+
//|                                                Optimization.mqh |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"

//+------------------------------------------------------------------+
//| Performance Metrics Structure                                    |
//+------------------------------------------------------------------+
struct SPerformanceMetrics
{
    double totalProfit;
    double totalLoss;
    double profitFactor;
    double sharpeRatio;
    double maxDrawdown;
    double winRate;
    double avgRiskReward;
    int totalTrades;
    int winningTrades;
    int losingTrades;
    double avgWin;
    double avgLoss;
    double recoveryFactor;
    double calmarRatio;
    datetime startDate;
    datetime endDate;
};

//+------------------------------------------------------------------+
//| Optimization Class                                               |
//+------------------------------------------------------------------+
class COptimization
{
private:
    string m_symbol;
    SPerformanceMetrics m_metrics;
    
    // Trade history arrays
    double m_tradeResults[];
    datetime m_tradeTimes[];
    double m_equityCurve[];
    
    // Optimization parameters
    bool m_enableOptimization;
    int m_optimizationPeriod;
    datetime m_lastOptimization;
    
    // Performance tracking
    double m_initialBalance;
    double m_currentBalance;
    double m_peakBalance;
    double m_currentDrawdown;
    double m_maxDrawdownValue;
    
public:
    COptimization();
    ~COptimization();
    
    bool Initialize(string symbol, double initialBalance);
    void Update();
    
    // Performance calculation
    void CalculateMetrics();
    void AddTradeResult(double profit, datetime tradeTime);
    void UpdateEquityCurve();
    
    // Optimization methods
    bool ShouldOptimize();
    void OptimizeParameters();
    bool IsPerformanceDegrading();
    
    // Getters
    SPerformanceMetrics GetMetrics() { return m_metrics; }
    double GetProfitFactor() { return m_metrics.profitFactor; }
    double GetSharpeRatio() { return m_metrics.sharpeRatio; }
    double GetMaxDrawdown() { return m_metrics.maxDrawdown; }
    double GetWinRate() { return m_metrics.winRate; }
    double GetCurrentDrawdown() { return m_currentDrawdown; }
    
    // Risk assessment
    bool IsDrawdownAcceptable(double maxAllowed = 15.0);
    bool IsPerformanceAcceptable();
    double GetRiskScore();
    
    // Reporting
    void PrintPerformanceReport();
    void SavePerformanceReport();
    string GetPerformanceSummary();
    
private:
    void ResetMetrics();
    double CalculateSharpeRatio();
    double CalculateDrawdown();
    double CalculateRecoveryFactor();
    double CalculateCalmarRatio();
    void UpdateDrawdown();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
COptimization::COptimization()
{
    m_symbol = "";
    m_enableOptimization = false;
    m_optimizationPeriod = 30; // 30 days
    m_lastOptimization = 0;
    
    m_initialBalance = 0;
    m_currentBalance = 0;
    m_peakBalance = 0;
    m_currentDrawdown = 0;
    m_maxDrawdownValue = 0;
    
    ResetMetrics();
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
COptimization::~COptimization()
{
    ArrayFree(m_tradeResults);
    ArrayFree(m_tradeTimes);
    ArrayFree(m_equityCurve);
}

//+------------------------------------------------------------------+
//| Initialize                                                       |
//+------------------------------------------------------------------+
bool COptimization::Initialize(string symbol, double initialBalance)
{
    m_symbol = symbol;
    m_initialBalance = initialBalance;
    m_currentBalance = initialBalance;
    m_peakBalance = initialBalance;
    
    ArrayResize(m_tradeResults, 0);
    ArrayResize(m_tradeTimes, 0);
    ArrayResize(m_equityCurve, 0);
    
    ResetMetrics();
    
    return true;
}

//+------------------------------------------------------------------+
//| Update optimization                                              |
//+------------------------------------------------------------------+
void COptimization::Update()
{
    // Update current balance
    m_currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    
    // Update drawdown
    UpdateDrawdown();
    
    // Update equity curve
    UpdateEquityCurve();
    
    // Calculate current metrics
    CalculateMetrics();
    
    // Check if optimization is needed
    if(ShouldOptimize())
    {
        OptimizeParameters();
        m_lastOptimization = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Calculate performance metrics                                    |
//+------------------------------------------------------------------+
void COptimization::CalculateMetrics()
{
    int totalTrades = ArraySize(m_tradeResults);
    if(totalTrades == 0)
        return;
    
    m_metrics.totalTrades = totalTrades;
    m_metrics.totalProfit = 0;
    m_metrics.totalLoss = 0;
    m_metrics.winningTrades = 0;
    m_metrics.losingTrades = 0;
    
    // Calculate basic metrics
    for(int i = 0; i < totalTrades; i++)
    {
        if(m_tradeResults[i] > 0)
        {
            m_metrics.totalProfit += m_tradeResults[i];
            m_metrics.winningTrades++;
        }
        else if(m_tradeResults[i] < 0)
        {
            m_metrics.totalLoss += MathAbs(m_tradeResults[i]);
            m_metrics.losingTrades++;
        }
    }
    
    // Calculate derived metrics
    m_metrics.winRate = (totalTrades > 0) ? (double)m_metrics.winningTrades / totalTrades * 100.0 : 0;
    m_metrics.profitFactor = (m_metrics.totalLoss > 0) ? m_metrics.totalProfit / m_metrics.totalLoss : 0;
    m_metrics.avgWin = (m_metrics.winningTrades > 0) ? m_metrics.totalProfit / m_metrics.winningTrades : 0;
    m_metrics.avgLoss = (m_metrics.losingTrades > 0) ? m_metrics.totalLoss / m_metrics.losingTrades : 0;
    m_metrics.avgRiskReward = (m_metrics.avgLoss > 0) ? m_metrics.avgWin / m_metrics.avgLoss : 0;
    
    // Calculate advanced metrics
    m_metrics.sharpeRatio = CalculateSharpeRatio();
    m_metrics.maxDrawdown = CalculateDrawdown();
    m_metrics.recoveryFactor = CalculateRecoveryFactor();
    m_metrics.calmarRatio = CalculateCalmarRatio();
    
    // Set date range
    if(totalTrades > 0)
    {
        m_metrics.startDate = m_tradeTimes[0];
        m_metrics.endDate = m_tradeTimes[totalTrades - 1];
    }
}

//+------------------------------------------------------------------+
//| Add trade result                                                 |
//+------------------------------------------------------------------+
void COptimization::AddTradeResult(double profit, datetime tradeTime)
{
    int size = ArraySize(m_tradeResults);
    ArrayResize(m_tradeResults, size + 1);
    ArrayResize(m_tradeTimes, size + 1);
    
    m_tradeResults[size] = profit;
    m_tradeTimes[size] = tradeTime;
    
    // Update balance
    m_currentBalance += profit;
    
    // Recalculate metrics
    CalculateMetrics();
}

//+------------------------------------------------------------------+
//| Update equity curve                                              |
//+------------------------------------------------------------------+
void COptimization::UpdateEquityCurve()
{
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    int size = ArraySize(m_equityCurve);
    ArrayResize(m_equityCurve, size + 1);
    m_equityCurve[size] = currentEquity;
    
    // Keep only recent data (last 1000 points)
    if(size > 1000)
    {
        for(int i = 0; i < 999; i++)
        {
            m_equityCurve[i] = m_equityCurve[i + 1];
        }
        ArrayResize(m_equityCurve, 1000);
    }
}

//+------------------------------------------------------------------+
//| Check if optimization should be performed                        |
//+------------------------------------------------------------------+
bool COptimization::ShouldOptimize()
{
    if(!m_enableOptimization)
        return false;
    
    // Check time since last optimization
    if(TimeCurrent() - m_lastOptimization < m_optimizationPeriod * 24 * 3600)
        return false;
    
    // Check if performance is degrading
    if(IsPerformanceDegrading())
        return true;
    
    // Check if enough trades have been made
    if(ArraySize(m_tradeResults) >= 20)
        return true;
    
    return false;
}

//+------------------------------------------------------------------+
//| Optimize parameters                                              |
//+------------------------------------------------------------------+
void COptimization::OptimizeParameters()
{
    Print("Starting parameter optimization...");
    
    // This is a placeholder for actual optimization logic
    // In a real implementation, you would:
    // 1. Test different parameter combinations
    // 2. Use genetic algorithms or grid search
    // 3. Validate on out-of-sample data
    // 4. Update EA parameters if improvement is found
    
    Print("Parameter optimization completed");
}

//+------------------------------------------------------------------+
//| Check if performance is degrading                                |
//+------------------------------------------------------------------+
bool COptimization::IsPerformanceDegrading()
{
    int recentTrades = 10;
    int totalTrades = ArraySize(m_tradeResults);
    
    if(totalTrades < recentTrades * 2)
        return false;
    
    // Calculate recent performance
    double recentProfit = 0;
    for(int i = totalTrades - recentTrades; i < totalTrades; i++)
    {
        recentProfit += m_tradeResults[i];
    }
    
    // Calculate earlier performance
    double earlierProfit = 0;
    for(int i = totalTrades - recentTrades * 2; i < totalTrades - recentTrades; i++)
    {
        earlierProfit += m_tradeResults[i];
    }
    
    // Check if recent performance is significantly worse
    return (recentProfit < earlierProfit * 0.5);
}

//+------------------------------------------------------------------+
//| Check if drawdown is acceptable                                  |
//+------------------------------------------------------------------+
bool COptimization::IsDrawdownAcceptable(double maxAllowed = 15.0)
{
    return (m_currentDrawdown <= maxAllowed);
}

//+------------------------------------------------------------------+
//| Check if overall performance is acceptable                       |
//+------------------------------------------------------------------+
bool COptimization::IsPerformanceAcceptable()
{
    if(m_metrics.totalTrades < 10)
        return true; // Not enough data
    
    // Check minimum performance criteria
    if(m_metrics.winRate < 50.0)
        return false;
    
    if(m_metrics.profitFactor < 1.2)
        return false;
    
    if(m_metrics.maxDrawdown > 20.0)
        return false;
    
    if(m_metrics.avgRiskReward < 1.5)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Get risk score                                                   |
//+------------------------------------------------------------------+
double COptimization::GetRiskScore()
{
    double score = 0;
    
    // Drawdown component (0-40 points)
    score += MathMax(0, 40 - m_currentDrawdown * 2);
    
    // Win rate component (0-30 points)
    score += MathMin(30, m_metrics.winRate * 0.5);
    
    // Profit factor component (0-20 points)
    score += MathMin(20, m_metrics.profitFactor * 10);
    
    // Sharpe ratio component (0-10 points)
    score += MathMin(10, m_metrics.sharpeRatio * 5);
    
    return MathMin(100, score);
}

//+------------------------------------------------------------------+
//| Print performance report                                         |
//+------------------------------------------------------------------+
void COptimization::PrintPerformanceReport()
{
    Print("=== ICT EA Performance Report ===");
    Print("Total Trades: ", m_metrics.totalTrades);
    Print("Win Rate: ", DoubleToString(m_metrics.winRate, 2), "%");
    Print("Profit Factor: ", DoubleToString(m_metrics.profitFactor, 2));
    Print("Sharpe Ratio: ", DoubleToString(m_metrics.sharpeRatio, 2));
    Print("Max Drawdown: ", DoubleToString(m_metrics.maxDrawdown, 2), "%");
    Print("Avg Risk-Reward: ", DoubleToString(m_metrics.avgRiskReward, 2));
    Print("Total Profit: $", DoubleToString(m_metrics.totalProfit, 2));
    Print("Total Loss: $", DoubleToString(m_metrics.totalLoss, 2));
    Print("Risk Score: ", DoubleToString(GetRiskScore(), 1), "/100");
    Print("================================");
}

//+------------------------------------------------------------------+
//| Get performance summary                                          |
//+------------------------------------------------------------------+
string COptimization::GetPerformanceSummary()
{
    return StringFormat("Trades: %d | Win Rate: %.1f%% | PF: %.2f | DD: %.1f%% | RR: %.2f",
                       m_metrics.totalTrades,
                       m_metrics.winRate,
                       m_metrics.profitFactor,
                       m_metrics.maxDrawdown,
                       m_metrics.avgRiskReward);
}

//+------------------------------------------------------------------+
//| Reset metrics                                                    |
//+------------------------------------------------------------------+
void COptimization::ResetMetrics()
{
    ZeroMemory(m_metrics);
    m_metrics.startDate = TimeCurrent();
    m_metrics.endDate = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Calculate Sharpe ratio                                           |
//+------------------------------------------------------------------+
double COptimization::CalculateSharpeRatio()
{
    int totalTrades = ArraySize(m_tradeResults);
    if(totalTrades < 2)
        return 0;
    
    // Calculate mean return
    double meanReturn = 0;
    for(int i = 0; i < totalTrades; i++)
    {
        meanReturn += m_tradeResults[i];
    }
    meanReturn /= totalTrades;
    
    // Calculate standard deviation
    double variance = 0;
    for(int i = 0; i < totalTrades; i++)
    {
        variance += MathPow(m_tradeResults[i] - meanReturn, 2);
    }
    variance /= (totalTrades - 1);
    double stdDev = MathSqrt(variance);
    
    // Calculate Sharpe ratio (assuming risk-free rate = 0)
    return (stdDev > 0) ? meanReturn / stdDev : 0;
}

//+------------------------------------------------------------------+
//| Calculate maximum drawdown                                       |
//+------------------------------------------------------------------+
double COptimization::CalculateDrawdown()
{
    return m_maxDrawdownValue;
}

//+------------------------------------------------------------------+
//| Calculate recovery factor                                        |
//+------------------------------------------------------------------+
double COptimization::CalculateRecoveryFactor()
{
    double netProfit = m_metrics.totalProfit - m_metrics.totalLoss;
    return (m_metrics.maxDrawdown > 0) ? netProfit / m_metrics.maxDrawdown : 0;
}

//+------------------------------------------------------------------+
//| Calculate Calmar ratio                                           |
//+------------------------------------------------------------------+
double COptimization::CalculateCalmarRatio()
{
    double annualReturn = (m_currentBalance - m_initialBalance) / m_initialBalance * 100;
    return (m_metrics.maxDrawdown > 0) ? annualReturn / m_metrics.maxDrawdown : 0;
}

//+------------------------------------------------------------------+
//| Update drawdown                                                  |
//+------------------------------------------------------------------+
void COptimization::UpdateDrawdown()
{
    if(m_currentBalance > m_peakBalance)
    {
        m_peakBalance = m_currentBalance;
        m_currentDrawdown = 0;
    }
    else
    {
        m_currentDrawdown = (m_peakBalance - m_currentBalance) / m_peakBalance * 100;
        if(m_currentDrawdown > m_maxDrawdownValue)
        {
            m_maxDrawdownValue = m_currentDrawdown;
        }
    }
}
