//+------------------------------------------------------------------+
//|                                             Compilation_Test.mq5 |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Compilation test for ICT Expert Advisor modules"

//--- Include all ICT modules to test compilation
#include "ICT_Modules\MarketStructure.mqh"
#include "ICT_Modules\LiquidityZones.mqh"
#include "ICT_Modules\OrderBlocks.mqh"
#include "ICT_Modules\FairValueGaps.mqh"
#include "ICT_Modules\RiskManagement.mqh"
#include "ICT_Modules\TradingLogic.mqh"
#include "ICT_Modules\ChartDrawing.mqh"
#include "ICT_Modules\Configuration.mqh"
#include "ICT_Modules\Optimization.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== ICT EA Compilation Test ===");
    
    // Test module instantiation
    CMarketStructure* testMS = new CMarketStructure();
    CLiquidityZones* testLZ = new CLiquidityZones();
    COrderBlocks* testOB = new COrderBlocks();
    CFairValueGaps* testFVG = new CFairValueGaps();
    CRiskManagement* testRM = new CRiskManagement();
    CTradingLogic* testTL = new CTradingLogic();
    CChartDrawing* testCD = new CChartDrawing();
    CConfiguration* testConfig = new CConfiguration();
    COptimization* testOpt = new COptimization();
    
    // Test initialization
    bool allInitialized = true;
    
    if(!testMS.Initialize(_Symbol, 20, 5))
    {
        Print("ERROR: MarketStructure initialization failed");
        allInitialized = false;
    }
    
    if(!testLZ.Initialize(_Symbol, 100, 2.0))
    {
        Print("ERROR: LiquidityZones initialization failed");
        allInitialized = false;
    }
    
    if(!testOB.Initialize(_Symbol, 50, 10.0, 100))
    {
        Print("ERROR: OrderBlocks initialization failed");
        allInitialized = false;
    }
    
    if(!testFVG.Initialize(_Symbol, 5.0, 50))
    {
        Print("ERROR: FairValueGaps initialization failed");
        allInitialized = false;
    }
    
    if(!testRM.Initialize(_Symbol, 1.0, 2.0, 3.0))
    {
        Print("ERROR: RiskManagement initialization failed");
        allInitialized = false;
    }
    
    if(!testTL.Initialize(_Symbol, 123456))
    {
        Print("ERROR: TradingLogic initialization failed");
        allInitialized = false;
    }
    
    if(!testCD.Initialize(_Symbol))
    {
        Print("ERROR: ChartDrawing initialization failed");
        allInitialized = false;
    }
    
    if(!testConfig.Initialize(_Symbol))
    {
        Print("ERROR: Configuration initialization failed");
        allInitialized = false;
    }
    
    if(!testOpt.Initialize(_Symbol, 10000.0))
    {
        Print("ERROR: Optimization initialization failed");
        allInitialized = false;
    }
    
    // Test basic functionality
    if(allInitialized)
    {
        Print("SUCCESS: All modules initialized successfully");
        
        // Test update functions
        testMS.Update();
        testLZ.Update();
        testOB.Update();
        testFVG.Update();
        testRM.Update();
        testOpt.Update();
        
        Print("SUCCESS: All module updates completed");
        
        // Test getter functions
        ENUM_TREND_DIRECTION trend = testMS.GetCurrentTrend();
        bool bosDetected = testMS.IsBOSDetected();
        int lzCount = testLZ.GetLiquidityZonesCount();
        int obCount = testOB.GetOrderBlocksCount();
        int fvgCount = testFVG.GetFVGCount();
        double riskPercent = testRM.GetRiskPercent();
        
        Print("SUCCESS: All getter functions working");
        Print("Trend: ", EnumToString(trend));
        Print("BOS Detected: ", bosDetected);
        Print("Liquidity Zones: ", lzCount);
        Print("Order Blocks: ", obCount);
        Print("Fair Value Gaps: ", fvgCount);
        Print("Risk Percent: ", riskPercent, "%");
    }
    
    // Clean up
    delete testMS;
    delete testLZ;
    delete testOB;
    delete testFVG;
    delete testRM;
    delete testTL;
    delete testCD;
    delete testConfig;
    delete testOpt;
    
    if(allInitialized)
    {
        Print("=== COMPILATION TEST PASSED ===");
        Print("All ICT EA modules compiled and initialized successfully!");
        Print("The EA is ready for deployment.");
    }
    else
    {
        Print("=== COMPILATION TEST FAILED ===");
        Print("Some modules failed to initialize properly.");
        return INIT_FAILED;
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("Compilation test completed. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // This is just a compilation test, no tick processing needed
    static bool testCompleted = false;
    
    if(!testCompleted)
    {
        Print("Tick processing test - EA is responsive");
        testCompleted = true;
    }
}
