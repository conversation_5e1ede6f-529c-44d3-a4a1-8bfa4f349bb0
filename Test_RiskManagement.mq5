//+------------------------------------------------------------------+
//|                                            Test_RiskManagement.mq5 |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"
#property version   "1.00"

// Test compilation of RiskManagement module
#include "ICT_Modules/RiskManagement.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Test RiskManagement initialization
    CRiskManagement riskManager;
    
    if(!riskManager.Initialize("EURUSD", 1.0, 2.0, 3.0))
    {
        Print("Failed to initialize RiskManagement");
        return INIT_FAILED;
    }
    
    Print("RiskManagement module compiled and initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("Test completed");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Test basic functionality
    static bool tested = false;
    if(!tested)
    {
        CRiskManagement riskManager;
        riskManager.Initialize("EURUSD", 1.0, 2.0, 3.0);
        
        // Test lot size calculation
        double lotSize = riskManager.CalculateLotSize(1.1000, 1.0950);
        Print("Calculated lot size: ", lotSize);
        
        // Test risk validation
        bool valid = riskManager.ValidateRiskReward(1.1000, 1.0950, 1.1100);
        Print("Risk-reward validation: ", valid);
        
        tested = true;
    }
}
