//+------------------------------------------------------------------+
//|                                                 TradingLogic.mqh |
//|                                    Copyright 2025, ICT Trader EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, ICT Trader EA"
#property link      "https://www.mql5.com"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//--- Forward declarations
class CMarketStructure;
class CLiquidityZones;
class COrderBlocks;
class CFairValueGaps;
class CRiskManagement;

//--- Include only necessary enums and structures
enum ENUM_TREND_DIRECTION
{
    TREND_NONE,
    TREND_BULLISH,
    TREND_BEARISH,
    TREND_SIDEWAYS
};

enum ENUM_LIQUIDITY_TYPE
{
    LIQUIDITY_NONE,
    LIQUIDITY_BUY_SIDE,
    LIQUIDITY_SELL_SIDE,
    LIQUIDITY_EQUAL_HIGHS,
    LIQUIDITY_EQUAL_LOWS
};

struct SStructurePoint
{
    datetime time;
    double price;
    int type;
    int barIndex;
    bool isValid;
    bool isBOS;
};

struct SOrderBlock
{
    datetime creationTime;
    double highPrice;
    double lowPrice;
    double openPrice;
    double closePrice;
    int type;
    int status;
    int barIndex;
    int strength;
    bool isValid;
    datetime lastTestTime;
    int testCount;
    bool isMitigated;
    datetime mitigationTime;
    double mitigationPrice;
};

struct SFairValueGap
{
    datetime creationTime;
    double upperLevel;
    double lowerLevel;
    double midLevel;
    int type;
    int status;
    int barIndex;
    double gapSize;
    bool isValid;
    datetime lastTestTime;
    int testCount;
    double fillPercentage;
    bool isPartiallyFilled;
    bool isFilled;
    datetime fillTime;
};

//+------------------------------------------------------------------+
//| Signal Types                                                     |
//+------------------------------------------------------------------+
enum ENUM_SIGNAL_TYPE
{
    SIGNAL_NONE,
    SIGNAL_BUY,
    SIGNAL_SELL
};

//+------------------------------------------------------------------+
//| Trading Signal Structure                                         |
//+------------------------------------------------------------------+
struct STradingSignal
{
    ENUM_SIGNAL_TYPE type;
    double entryPrice;
    double stopLoss;
    double takeProfit;
    double lotSize;
    string comment;
    bool isValid;
    datetime signalTime;
    
    // ICT specific data
    bool hasOrderBlock;
    bool hasFVG;
    bool hasLiquiditySweep;
    bool hasStructureConfirmation;
    double riskRewardRatio;
};

//+------------------------------------------------------------------+
//| Trading Logic Class                                              |
//+------------------------------------------------------------------+
class CTradingLogic
{
private:
    string m_symbol;
    int m_magicNumber;
    
    CTrade m_trade;
    CPositionInfo m_position;
    
    STradingSignal m_lastSignal;
    datetime m_lastSignalTime;
    
    // External parameters (set from main EA)
    bool m_useMarketStructure;
    bool m_useOrderBlocks;
    bool m_useFairValueGaps;
    bool m_useLiquidityZones;
    bool m_requireStructureConfirmation;
    bool m_requireLiquiditySweep;
    
public:
    CTradingLogic();
    ~CTradingLogic();
    
    bool Initialize(string symbol, int magicNumber);
    void SetParameters(bool useStructure, bool useOB, bool useFVG, bool useLiquidity, 
                      bool requireStructure, bool requireLiquiditySweep);
    
    // Main trading methods
    void CheckForSignals(CMarketStructure* marketStructure, CLiquidityZones* liquidityZones,
                        COrderBlocks* orderBlocks, CFairValueGaps* fairValueGaps);
    
    bool AnalyzeBuySignal(CMarketStructure* marketStructure, CLiquidityZones* liquidityZones,
                         COrderBlocks* orderBlocks, CFairValueGaps* fairValueGaps, STradingSignal &signal);
    
    bool AnalyzeSellSignal(CMarketStructure* marketStructure, CLiquidityZones* liquidityZones,
                          COrderBlocks* orderBlocks, CFairValueGaps* fairValueGaps, STradingSignal &signal);
    
    bool ExecuteSignal(STradingSignal &signal, CRiskManagement* riskManager);
    
    // Trade management
    void ManageOpenTrades(bool useTrailingStop, double trailingATR);
    void UpdateTrailingStop(ulong ticket, bool useTrailingStop, double trailingATR);
    void CheckTakeProfit();
    void CheckStopLoss();
    
    // Signal validation
    bool ValidateSignal(STradingSignal &signal);
    bool CheckConfirmations(STradingSignal &signal);
    bool IsEngulfingPattern(int barIndex);
    bool IsPinBar(int barIndex);
    
    // Utility methods
    double CalculateStopLoss(ENUM_SIGNAL_TYPE signalType, CMarketStructure* marketStructure, 
                           CLiquidityZones* liquidityZones);
    double CalculateTakeProfit(ENUM_SIGNAL_TYPE signalType, double entryPrice, double stopLoss, 
                             double riskRewardRatio);
    
    // Getters
    STradingSignal GetLastSignal() { return m_lastSignal; }
    datetime GetLastSignalTime() { return m_lastSignalTime; }
    
private:
    bool CheckBullishStructure(CMarketStructure* marketStructure);
    bool CheckBearishStructure(CMarketStructure* marketStructure);
    bool CheckOrderBlockEntry(COrderBlocks* orderBlocks, ENUM_SIGNAL_TYPE signalType);
    bool CheckFVGEntry(CFairValueGaps* fairValueGaps, ENUM_SIGNAL_TYPE signalType);
    bool CheckLiquidityEntry(CLiquidityZones* liquidityZones, ENUM_SIGNAL_TYPE signalType);
    
    double GetCurrentPrice();
    double GetPointValue();
    string GenerateComment(STradingSignal &signal);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CTradingLogic::CTradingLogic()
{
    m_symbol = "";
    m_magicNumber = 0;
    m_lastSignalTime = 0;
    
    m_useMarketStructure = true;
    m_useOrderBlocks = true;
    m_useFairValueGaps = true;
    m_useLiquidityZones = true;
    m_requireStructureConfirmation = true;
    m_requireLiquiditySweep = false;
    
    ZeroMemory(m_lastSignal);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CTradingLogic::~CTradingLogic()
{
}

//+------------------------------------------------------------------+
//| Initialize                                                       |
//+------------------------------------------------------------------+
bool CTradingLogic::Initialize(string symbol, int magicNumber)
{
    m_symbol = symbol;
    m_magicNumber = magicNumber;
    
    m_trade.SetExpertMagicNumber(magicNumber);
    m_trade.SetMarginMode();
    m_trade.SetTypeFillingBySymbol(symbol);
    
    return true;
}

//+------------------------------------------------------------------+
//| Set parameters                                                   |
//+------------------------------------------------------------------+
void CTradingLogic::SetParameters(bool useStructure, bool useOB, bool useFVG, bool useLiquidity, 
                                 bool requireStructure, bool requireLiquiditySweep)
{
    m_useMarketStructure = useStructure;
    m_useOrderBlocks = useOB;
    m_useFairValueGaps = useFVG;
    m_useLiquidityZones = useLiquidity;
    m_requireStructureConfirmation = requireStructure;
    m_requireLiquiditySweep = requireLiquiditySweep;
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CTradingLogic::CheckForSignals(CMarketStructure* marketStructure, CLiquidityZones* liquidityZones,
                                   COrderBlocks* orderBlocks, CFairValueGaps* fairValueGaps)
{
    STradingSignal buySignal, sellSignal;
    ZeroMemory(buySignal);
    ZeroMemory(sellSignal);
    
    // Analyze buy signals
    if(AnalyzeBuySignal(marketStructure, liquidityZones, orderBlocks, fairValueGaps, buySignal))
    {
        if(ValidateSignal(buySignal))
        {
            m_lastSignal = buySignal;
            m_lastSignalTime = TimeCurrent();
            
            Print("BUY Signal detected: ", buySignal.comment);
            // Signal will be executed by the main EA
        }
    }
    
    // Analyze sell signals
    if(AnalyzeSellSignal(marketStructure, liquidityZones, orderBlocks, fairValueGaps, sellSignal))
    {
        if(ValidateSignal(sellSignal))
        {
            m_lastSignal = sellSignal;
            m_lastSignalTime = TimeCurrent();
            
            Print("SELL Signal detected: ", sellSignal.comment);
            // Signal will be executed by the main EA
        }
    }
}

//+------------------------------------------------------------------+
//| Analyze buy signal                                               |
//+------------------------------------------------------------------+
bool CTradingLogic::AnalyzeBuySignal(CMarketStructure* marketStructure, CLiquidityZones* liquidityZones,
                                    COrderBlocks* orderBlocks, CFairValueGaps* fairValueGaps, STradingSignal &signal)
{
    signal.type = SIGNAL_BUY;
    signal.entryPrice = GetCurrentPrice();
    signal.signalTime = TimeCurrent();
    signal.isValid = false;
    
    // Check market structure for bullish bias
    if(m_useMarketStructure && m_requireStructureConfirmation)
    {
        if(!CheckBullishStructure(marketStructure))
            return false;
        signal.hasStructureConfirmation = true;
    }
    
    // Check for liquidity sweep (optional)
    if(m_requireLiquiditySweep && m_useLiquidityZones)
    {
        if(!liquidityZones->IsLiquiditySweptRecently(LIQUIDITY_SELL_SIDE, 5))
            return false;
        signal.hasLiquiditySweep = true;
    }
    
    // Check for order block entry
    if(m_useOrderBlocks)
    {
        if(CheckOrderBlockEntry(orderBlocks, SIGNAL_BUY))
        {
            signal.hasOrderBlock = true;
        }
    }
    
    // Check for FVG entry
    if(m_useFairValueGaps)
    {
        if(CheckFVGEntry(fairValueGaps, SIGNAL_BUY))
        {
            signal.hasFVG = true;
        }
    }
    
    // Check for liquidity entry
    if(m_useLiquidityZones)
    {
        if(CheckLiquidityEntry(liquidityZones, SIGNAL_BUY))
        {
            // Additional confirmation
        }
    }
    
    // Must have at least one entry confirmation
    if(!signal.hasOrderBlock && !signal.hasFVG)
        return false;
    
    // Calculate stop loss and take profit
    signal.stopLoss = CalculateStopLoss(SIGNAL_BUY, marketStructure, liquidityZones);
    if(signal.stopLoss <= 0 || signal.stopLoss >= signal.entryPrice)
        return false;
    
    signal.takeProfit = CalculateTakeProfit(SIGNAL_BUY, signal.entryPrice, signal.stopLoss, 2.0);
    signal.riskRewardRatio = (signal.takeProfit - signal.entryPrice) / (signal.entryPrice - signal.stopLoss);
    
    signal.comment = GenerateComment(signal);
    signal.isValid = true;
    
    return true;
}

//+------------------------------------------------------------------+
//| Analyze sell signal                                              |
//+------------------------------------------------------------------+
bool CTradingLogic::AnalyzeSellSignal(CMarketStructure* marketStructure, CLiquidityZones* liquidityZones,
                                     COrderBlocks* orderBlocks, CFairValueGaps* fairValueGaps, STradingSignal &signal)
{
    signal.type = SIGNAL_SELL;
    signal.entryPrice = GetCurrentPrice();
    signal.signalTime = TimeCurrent();
    signal.isValid = false;
    
    // Check market structure for bearish bias
    if(m_useMarketStructure && m_requireStructureConfirmation)
    {
        if(!CheckBearishStructure(marketStructure))
            return false;
        signal.hasStructureConfirmation = true;
    }
    
    // Check for liquidity sweep (optional)
    if(m_requireLiquiditySweep && m_useLiquidityZones)
    {
        if(!liquidityZones->IsLiquiditySweptRecently(LIQUIDITY_BUY_SIDE, 5))
            return false;
        signal.hasLiquiditySweep = true;
    }
    
    // Check for order block entry
    if(m_useOrderBlocks)
    {
        if(CheckOrderBlockEntry(orderBlocks, SIGNAL_SELL))
        {
            signal.hasOrderBlock = true;
        }
    }
    
    // Check for FVG entry
    if(m_useFairValueGaps)
    {
        if(CheckFVGEntry(fairValueGaps, SIGNAL_SELL))
        {
            signal.hasFVG = true;
        }
    }
    
    // Check for liquidity entry
    if(m_useLiquidityZones)
    {
        if(CheckLiquidityEntry(liquidityZones, SIGNAL_SELL))
        {
            // Additional confirmation
        }
    }
    
    // Must have at least one entry confirmation
    if(!signal.hasOrderBlock && !signal.hasFVG)
        return false;
    
    // Calculate stop loss and take profit
    signal.stopLoss = CalculateStopLoss(SIGNAL_SELL, marketStructure, liquidityZones);
    if(signal.stopLoss <= 0 || signal.stopLoss <= signal.entryPrice)
        return false;
    
    signal.takeProfit = CalculateTakeProfit(SIGNAL_SELL, signal.entryPrice, signal.stopLoss, 2.0);
    signal.riskRewardRatio = (signal.entryPrice - signal.takeProfit) / (signal.stopLoss - signal.entryPrice);
    
    signal.comment = GenerateComment(signal);
    signal.isValid = true;
    
    return true;
}

//+------------------------------------------------------------------+
//| Execute trading signal                                           |
//+------------------------------------------------------------------+
bool CTradingLogic::ExecuteSignal(STradingSignal &signal, CRiskManagement* riskManager)
{
    if(!signal.isValid || riskManager == NULL)
        return false;
    
    // Check if we can open new trade
    if(!riskManager->CanOpenNewTrade())
    {
        Print("Cannot open new trade - risk limits exceeded");
        return false;
    }

    // Calculate lot size
    signal.lotSize = riskManager->CalculateLotSize(signal.entryPrice, signal.stopLoss);
    if(signal.lotSize <= 0)
    {
        Print("Invalid lot size calculated");
        return false;
    }

    // Validate risk-reward ratio
    if(!riskManager->ValidateRiskReward(signal.entryPrice, signal.stopLoss, signal.takeProfit))
    {
        Print("Risk-reward ratio validation failed");
        return false;
    }
    
    // Execute the trade
    bool result = false;
    
    if(signal.type == SIGNAL_BUY)
    {
        result = m_trade.Buy(signal.lotSize, m_symbol, signal.entryPrice, 
                           signal.stopLoss, signal.takeProfit, signal.comment);
    }
    else if(signal.type == SIGNAL_SELL)
    {
        result = m_trade.Sell(signal.lotSize, m_symbol, signal.entryPrice, 
                            signal.stopLoss, signal.takeProfit, signal.comment);
    }
    
    if(result)
    {
        Print("Trade executed successfully: ", signal.comment);
        Print("Entry: ", signal.entryPrice, " SL: ", signal.stopLoss, " TP: ", signal.takeProfit);
        Print("Lot size: ", signal.lotSize, " RR: ", signal.riskRewardRatio);
    }
    else
    {
        Print("Trade execution failed: ", m_trade.ResultRetcode(), " - ", m_trade.ResultRetcodeDescription());
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| Manage open trades                                               |
//+------------------------------------------------------------------+
void CTradingLogic::ManageOpenTrades(bool useTrailingStop, double trailingATR)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(m_position.SelectByIndex(i))
        {
            if(m_position.Symbol() == m_symbol && m_position.Magic() == m_magicNumber)
            {
                ulong ticket = m_position.Ticket();
                
                // Update trailing stop
                if(useTrailingStop)
                {
                    UpdateTrailingStop(ticket, useTrailingStop, trailingATR);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Update trailing stop                                             |
//+------------------------------------------------------------------+
void CTradingLogic::UpdateTrailingStop(ulong ticket, bool useTrailingStop, double trailingATR)
{
    if(!m_position.SelectByTicket(ticket))
        return;
    
    double currentPrice = GetCurrentPrice();
    double currentSL = m_position.StopLoss();
    double newSL = 0;
    
    // Calculate new trailing stop based on ATR
    int atrHandle = iATR(m_symbol, PERIOD_CURRENT, 14);
    if(atrHandle == INVALID_HANDLE)
        return;
    
    double atrBuffer[];
    if(CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) <= 0)
    {
        IndicatorRelease(atrHandle);
        return;
    }
    
    double atrValue = atrBuffer[0];
    IndicatorRelease(atrHandle);
    
    if(m_position.PositionType() == POSITION_TYPE_BUY)
    {
        newSL = currentPrice - (atrValue * trailingATR);
        
        // Only move SL up
        if(newSL > currentSL && newSL < currentPrice)
        {
            m_trade.PositionModify(ticket, newSL, m_position.TakeProfit());
        }
    }
    else if(m_position.PositionType() == POSITION_TYPE_SELL)
    {
        newSL = currentPrice + (atrValue * trailingATR);
        
        // Only move SL down
        if(newSL < currentSL && newSL > currentPrice)
        {
            m_trade.PositionModify(ticket, newSL, m_position.TakeProfit());
        }
    }
}

//+------------------------------------------------------------------+
//| Validate trading signal                                          |
//+------------------------------------------------------------------+
bool CTradingLogic::ValidateSignal(STradingSignal &signal)
{
    // Check basic signal validity
    if(!signal.isValid)
        return false;
    
    // Check entry price
    if(signal.entryPrice <= 0)
        return false;
    
    // Check stop loss
    if(signal.stopLoss <= 0)
        return false;
    
    // Check take profit
    if(signal.takeProfit <= 0)
        return false;
    
    // Check risk-reward ratio
    if(signal.riskRewardRatio < 1.5)
        return false;
    
    // Check confirmations
    return CheckConfirmations(signal);
}

//+------------------------------------------------------------------+
//| Check signal confirmations                                       |
//+------------------------------------------------------------------+
bool CTradingLogic::CheckConfirmations(STradingSignal &signal)
{
    int confirmations = 0;
    
    if(signal.hasStructureConfirmation)
        confirmations++;
    
    if(signal.hasOrderBlock)
        confirmations++;
    
    if(signal.hasFVG)
        confirmations++;
    
    if(signal.hasLiquiditySweep)
        confirmations++;
    
    // Require at least 2 confirmations
    return (confirmations >= 2);
}

//+------------------------------------------------------------------+
//| Check bullish market structure                                   |
//+------------------------------------------------------------------+
bool CTradingLogic::CheckBullishStructure(CMarketStructure* marketStructure)
{
    if(marketStructure == NULL)
        return false;
    
    return (marketStructure->GetCurrentTrend() == TREND_BULLISH ||
            (marketStructure->IsBOSDetected() && marketStructure->GetBOSDirection() == TREND_BULLISH));
}

//+------------------------------------------------------------------+
//| Check bearish market structure                                   |
//+------------------------------------------------------------------+
bool CTradingLogic::CheckBearishStructure(CMarketStructure* marketStructure)
{
    if(marketStructure == NULL)
        return false;
    
    return (marketStructure->GetCurrentTrend() == TREND_BEARISH ||
            (marketStructure->IsBOSDetected() && marketStructure->GetBOSDirection() == TREND_BEARISH));
}

//+------------------------------------------------------------------+
//| Check order block entry                                          |
//+------------------------------------------------------------------+
bool CTradingLogic::CheckOrderBlockEntry(COrderBlocks* orderBlocks, ENUM_SIGNAL_TYPE signalType)
{
    if(orderBlocks == NULL)
        return false;
    
    double currentPrice = GetCurrentPrice();
    
    if(signalType == SIGNAL_BUY)
    {
        SOrderBlock bullishOB = orderBlocks->GetNearestBullishOrderBlock(currentPrice);
        return (bullishOB.isValid && orderBlocks->IsPriceInOrderBlock(currentPrice, bullishOB));
    }
    else if(signalType == SIGNAL_SELL)
    {
        SOrderBlock bearishOB = orderBlocks->GetNearestBearishOrderBlock(currentPrice);
        return (bearishOB.isValid && orderBlocks->IsPriceInOrderBlock(currentPrice, bearishOB));
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Check FVG entry                                                  |
//+------------------------------------------------------------------+
bool CTradingLogic::CheckFVGEntry(CFairValueGaps* fairValueGaps, ENUM_SIGNAL_TYPE signalType)
{
    if(fairValueGaps == NULL)
        return false;
    
    double currentPrice = GetCurrentPrice();
    
    if(signalType == SIGNAL_BUY)
    {
        SFairValueGap bullishFVG = fairValueGaps->GetNearestBullishFVG(currentPrice);
        return (bullishFVG.isValid && fairValueGaps->IsPriceInFVG(currentPrice, bullishFVG));
    }
    else if(signalType == SIGNAL_SELL)
    {
        SFairValueGap bearishFVG = fairValueGaps->GetNearestBearishFVG(currentPrice);
        return (bearishFVG.isValid && fairValueGaps->IsPriceInFVG(currentPrice, bearishFVG));
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Check liquidity entry                                            |
//+------------------------------------------------------------------+
bool CTradingLogic::CheckLiquidityEntry(CLiquidityZones* liquidityZones, ENUM_SIGNAL_TYPE signalType)
{
    if(liquidityZones == NULL)
        return false;
    
    // Check for recent liquidity sweeps in the opposite direction
    if(signalType == SIGNAL_BUY)
    {
        return liquidityZones->IsLiquiditySweptRecently(LIQUIDITY_SELL_SIDE, 10);
    }
    else if(signalType == SIGNAL_SELL)
    {
        return liquidityZones->IsLiquiditySweptRecently(LIQUIDITY_BUY_SIDE, 10);
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Calculate stop loss                                              |
//+------------------------------------------------------------------+
double CTradingLogic::CalculateStopLoss(ENUM_SIGNAL_TYPE signalType, CMarketStructure* marketStructure, 
                                       CLiquidityZones* liquidityZones)
{
    double stopLoss = 0;
    double buffer = 5 * GetPointValue(); // 5 point buffer
    
    if(signalType == SIGNAL_BUY)
    {
        // Place SL below recent low or liquidity level
        if(liquidityZones != NULL)
        {
            double sellSideLiquidity = liquidityZones->GetNearestSellSideLiquidity();
            if(sellSideLiquidity > 0)
                stopLoss = sellSideLiquidity - buffer;
        }
        
        if(stopLoss <= 0 && marketStructure != NULL)
        {
            SStructurePoint lastLow = marketStructure->GetLastLow();
            if(lastLow.isValid)
                stopLoss = lastLow.price - buffer;
        }
        
        // Fallback to recent swing low
        if(stopLoss <= 0)
        {
            double recentLow = iLow(m_symbol, PERIOD_CURRENT, iLowest(m_symbol, PERIOD_CURRENT, MODE_LOW, 20, 1));
            stopLoss = recentLow - buffer;
        }
    }
    else if(signalType == SIGNAL_SELL)
    {
        // Place SL above recent high or liquidity level
        if(liquidityZones != NULL)
        {
            double buySideLiquidity = liquidityZones->GetNearestBuySideLiquidity();
            if(buySideLiquidity > 0)
                stopLoss = buySideLiquidity + buffer;
        }
        
        if(stopLoss <= 0 && marketStructure != NULL)
        {
            SStructurePoint lastHigh = marketStructure->GetLastHigh();
            if(lastHigh.isValid)
                stopLoss = lastHigh.price + buffer;
        }
        
        // Fallback to recent swing high
        if(stopLoss <= 0)
        {
            double recentHigh = iHigh(m_symbol, PERIOD_CURRENT, iHighest(m_symbol, PERIOD_CURRENT, MODE_HIGH, 20, 1));
            stopLoss = recentHigh + buffer;
        }
    }
    
    return stopLoss;
}

//+------------------------------------------------------------------+
//| Calculate take profit                                            |
//+------------------------------------------------------------------+
double CTradingLogic::CalculateTakeProfit(ENUM_SIGNAL_TYPE signalType, double entryPrice, double stopLoss, 
                                         double riskRewardRatio)
{
    double risk = MathAbs(entryPrice - stopLoss);
    double reward = risk * riskRewardRatio;
    
    if(signalType == SIGNAL_BUY)
    {
        return entryPrice + reward;
    }
    else if(signalType == SIGNAL_SELL)
    {
        return entryPrice - reward;
    }
    
    return 0;
}

//+------------------------------------------------------------------+
//| Get current price                                                |
//+------------------------------------------------------------------+
double CTradingLogic::GetCurrentPrice()
{
    return SymbolInfoDouble(m_symbol, SYMBOL_BID);
}

//+------------------------------------------------------------------+
//| Get point value                                                  |
//+------------------------------------------------------------------+
double CTradingLogic::GetPointValue()
{
    return SymbolInfoDouble(m_symbol, SYMBOL_POINT);
}

//+------------------------------------------------------------------+
//| Generate comment for trade                                       |
//+------------------------------------------------------------------+
string CTradingLogic::GenerateComment(STradingSignal &signal)
{
    string comment = "ICT_EA_";
    
    if(signal.type == SIGNAL_BUY)
        comment += "BUY";
    else
        comment += "SELL";
    
    if(signal.hasOrderBlock)
        comment += "_OB";
    
    if(signal.hasFVG)
        comment += "_FVG";
    
    if(signal.hasLiquiditySweep)
        comment += "_LS";
    
    if(signal.hasStructureConfirmation)
        comment += "_STR";
    
    return comment;
}
